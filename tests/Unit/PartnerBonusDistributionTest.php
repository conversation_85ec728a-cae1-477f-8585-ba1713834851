<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\BonusHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class PartnerBonusDistributionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 設置測試數據庫連接
        config(['database.connections.main_db' => config('database.connections.mysql')]);
    }

    /**
     * 測試多人合夥平級獎勵分配功能
     */
    public function test_partner_bonus_distribution_to_multiple_uplines()
    {
        // 創建測試用的合夥人等級數據
        $this->createPartnerLevels();
        
        // 創建測試用戶層級結構
        $users = $this->createUserHierarchy();
        
        // 創建 BonusHelper 實例
        $bonusHelper = new BonusHelper();
        
        // 初始化所有用戶
        foreach ($users as $userId => $userData) {
            $bonusHelper->init_user_set($userId, $userData);
        }
        
        // 測試分配邏輯
        $baseCV = 1000.0;
        $partnerId = 4; // 最底層的合夥人
        
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass(OrderHelper::class);
        $method = $reflection->getMethod('distributePartnerBonusToUplines');
        $method->setAccessible(true);
        
        // 執行分配
        $method->invoke(null, $bonusHelper, $partnerId, $baseCV);
        
        // 驗證分配結果
        $userCal = $bonusHelper->get('user_cal');
        
        // 用戶2 (創業合夥人, 10%): 應該獲得 1000 * (0.1 / 0.25) = 400
        $this->assertEquals(400.0, $userCal[2]['total_available_cv'], '創業合夥人應該獲得400 CV');
        
        // 用戶1 (準合夥人, 15%): 應該獲得 1000 * (0.15 / 0.25) = 600  
        $this->assertEquals(600.0, $userCal[1]['total_available_cv'], '準合夥人應該獲得600 CV');
    }

    /**
     * 測試獲取上線合夥人功能
     */
    public function test_get_upline_partners()
    {
        // 創建測試數據
        $this->createPartnerLevels();
        $users = $this->createUserHierarchy();
        
        $bonusHelper = new BonusHelper();
        foreach ($users as $userId => $userData) {
            $bonusHelper->init_user_set($userId, $userData);
        }
        
        // 使用反射測試私有方法
        $reflection = new \ReflectionClass(OrderHelper::class);
        $method = $reflection->getMethod('getUplinePartners');
        $method->setAccessible(true);
        
        // 測試從用戶4開始獲取上線合夥人
        $uplinePartners = $method->invoke(null, $bonusHelper, 4);
        
        // 應該返回 [3, 2, 1] (按從近到遠的順序)
        $this->assertEquals([3, 2, 1], $uplinePartners, '應該獲取到所有上線合夥人');
    }

    /**
     * 創建合夥人等級測試數據
     */
    private function createPartnerLevels()
    {
        DB::connection('main_db')->table('partner_level')->insert([
            ['id' => 1, 'name' => '準合夥人', 'ratio' => 1.75, 'contribution' => 1320, 'partner_bonus_ratio' => 15, 'orderform_ad_weight' => 4],
            ['id' => 2, 'name' => '創業合夥人', 'ratio' => 1.5, 'contribution' => 400, 'partner_bonus_ratio' => 10, 'orderform_ad_weight' => 2],
            ['id' => 3, 'name' => '微合夥人', 'ratio' => 1.25, 'contribution' => 132, 'partner_bonus_ratio' => 5, 'orderform_ad_weight' => 1],
        ]);
    }

    /**
     * 創建用戶層級結構測試數據
     * 
     * 結構: 1(準合夥人) <- 2(創業合夥人) <- 3(微合夥人) <- 4(微合夥人) <- 5(非合夥人)
     */
    private function createUserHierarchy()
    {
        return [
            1 => [
                'id' => 1,
                'upline_user' => 0,
                'partner_level_id' => 1, // 準合夥人
                'registration_from' => 1,
            ],
            2 => [
                'id' => 2,
                'upline_user' => 1,
                'partner_level_id' => 2, // 創業合夥人
                'registration_from' => 1,
            ],
            3 => [
                'id' => 3,
                'upline_user' => 2,
                'partner_level_id' => 3, // 微合夥人
                'registration_from' => 1,
            ],
            4 => [
                'id' => 4,
                'upline_user' => 3,
                'partner_level_id' => 3, // 微合夥人
                'registration_from' => 1,
            ],
            5 => [
                'id' => 5,
                'upline_user' => 4,
                'partner_level_id' => 0, // 非合夥人
                'registration_from' => 1,
            ],
        ];
    }
}
