# 合夥平級獎勵多人分配功能修改說明

## 修改概述

將原本的單人合夥平級獎勵分配機制改為多人分配機制。當一個合夥人獲得推廣獎勵時，其所有上線合夥人都會依照自己的合夥人階級比例領取合夥平級獎勵。

## 修改的文件

### 1. `app/Services/pattern/OrderHelper.php`

#### 修改位置1: 廣告會員購買課程的推廣獎勵分配 (第2140-2141行)
**原邏輯:**
```php
/** 有拿到推廣獎勵的上線，如果是合夥人，則上線依自已的合夥人階級依% 領取合夥平級獎勵 */
$top_line_partner_level_id = $BonusHelper->get('user_cal')[$top_line_user]['data']['partner_level_id'] ?? 0;
if ($top_line_partner_level_id > 0) {
    /*上線的上線是合夥人，依照合夥平級獎勵可回饋比率分配*/
    $partner_level = $BonusHelper->get('arr_partner_levels')[$top_line_partner_level_id] ?? [];
    $partner_bonus_ratio = ($partner_level['partner_bonus_ratio'] ?? 0) / 100;
    $final_available_cv = $share_available_cv * $partner_bonus_ratio;
    $BonusHelper->add_available_cv($top_line_user, $final_available_cv);
}
```

**新邏輯:**
```php
/** 有拿到推廣獎勵的合夥人，其所有上線合夥人依自己的合夥人階級比例領取合夥平級獎勵 */
self::distributePartnerBonusToUplines($BonusHelper, $partner_id, $share_available_cv);
```

#### 修改位置2: 直推會員購買課程的合夥平級獎勵分配 (第2159-2165行)
**原邏輯:**
```php
//20250807增加: 上線的上線，必須也要是合夥人，且依照partner_level的合夥平級獎勵可回饋比率分配
/*檢查上線的上線是否為合夥人*/
$top_line_partner_level_id = $BonusHelper->get('user_cal')[$top_line_user]['data']['partner_level_id'] ?? 0;
if ($top_line_partner_level_id > 0) {
    /*上線的上線是合夥人，依照合夥平級獎勵可回饋比率分配*/
    $partner_level = $BonusHelper->get('arr_partner_levels')[$top_line_partner_level_id] ?? [];
    $partner_bonus_ratio = ($partner_level['partner_bonus_ratio'] ?? 0) / 100;
    $final_available_cv = $available_cv * $partner_bonus_ratio;
    $BonusHelper->add_available_cv($top_line_user, $final_available_cv);
}
```

**新邏輯:**
```php
//20250807修改: 分配到推廣獎的合夥人，如果上線也是合夥人，上線依自己的合夥人階級比例領取合夥獎勵，現在改為多人分配
/*檢查購買者的推薦者是否為合夥人*/
$upline_partner_level_id = $BonusHelper->get('user_cal')[$upline_user]['data']['partner_level_id'] ?? 0;
if ($upline_partner_level_id > 0) {
    /*購買者的推薦者是合夥人，分配給其所有上線合夥人*/
    self::distributePartnerBonusToUplines($BonusHelper, $upline_user, $available_cv);
}
```

#### 新增方法1: `distributePartnerBonusToUplines()` (第2478-2514行)
```php
/**
 * 分配合夥平級獎勵給所有上線合夥人
 * 
 * @param BonusHelper $BonusHelper
 * @param int $partner_id 獲得推廣獎勵的合夥人ID
 * @param float $base_cv 基礎CV金額
 */
private static function distributePartnerBonusToUplines($BonusHelper, int $partner_id, float $base_cv)
{
    // 獲取所有上線合夥人
    $upline_partners = self::getUplinePartners($BonusHelper, $partner_id);
    
    if (empty($upline_partners)) {
        return; // 沒有上線合夥人，直接返回
    }

    // 計算總權重
    $total_weight = 0;
    $partner_weights = [];
    
    foreach ($upline_partners as $upline_id) {
        $partner_level_id = $BonusHelper->get('user_cal')[$upline_id]['data']['partner_level_id'] ?? 0;
        if ($partner_level_id > 0) {
            $partner_level = $BonusHelper->get('arr_partner_levels')[$partner_level_id] ?? [];
            $partner_bonus_ratio = ($partner_level['partner_bonus_ratio'] ?? 0) / 100;
            
            if ($partner_bonus_ratio > 0) {
                $partner_weights[$upline_id] = $partner_bonus_ratio;
                $total_weight += $partner_bonus_ratio;
            }
        }
    }

    // 如果沒有有效的權重，直接返回
    if ($total_weight <= 0) {
        return;
    }

    // 按權重分配CV
    foreach ($partner_weights as $upline_id => $weight) {
        $allocated_cv = $base_cv * ($weight / $total_weight);
        $BonusHelper->add_available_cv($upline_id, $allocated_cv);
    }
}
```

#### 新增方法2: `getUplinePartners()` (第2523-2554行)
```php
/**
 * 獲取指定合夥人的所有上線合夥人
 * 
 * @param BonusHelper $BonusHelper
 * @param int $partner_id 合夥人ID
 * @return array 上線合夥人ID陣列
 */
private static function getUplinePartners($BonusHelper, int $partner_id): array
{
    $upline_partners = [];
    $current_user_id = $partner_id;
    $max_levels = 10; // 防止無限循環，最多查找10層
    $level_count = 0;

    while ($level_count < $max_levels) {
        // 獲取當前用戶的上線
        $upline_user_id = $BonusHelper->get('user_cal')[$current_user_id]['data']['upline_user'] ?? 0;
        
        if ($upline_user_id <= 0) {
            break; // 沒有上線了
        }

        // 初始化上線用戶數據
        $BonusHelper->init_user_set($upline_user_id);
        
        // 檢查上線是否為合夥人
        $upline_partner_level_id = $BonusHelper->get('user_cal')[$upline_user_id]['data']['partner_level_id'] ?? 0;
        
        if ($upline_partner_level_id > 0) {
            $upline_partners[] = $upline_user_id;
        }

        // 繼續向上查找
        $current_user_id = $upline_user_id;
        $level_count++;
    }

    return $upline_partners;
}
```

## 分配邏輯說明

### 舊邏輯
- 只有直接上線合夥人獲得獎勵
- 獎勵金額 = 基礎CV × 上線合夥人的partner_bonus_ratio

### 新邏輯
1. 找出獲得推廣獎勵的合夥人的所有上線合夥人
2. 計算每個上線合夥人的權重（partner_bonus_ratio）
3. 按權重比例分配基礎CV金額

### 分配公式
```
上線合夥人獲得的CV = 基礎CV × (該合夥人的partner_bonus_ratio / 所有上線合夥人的partner_bonus_ratio總和)
```

## 示例場景

假設用戶層級結構：
- 用戶1 (準合夥人, 15%) <- 用戶2 (創業合夥人, 10%) <- 用戶3 (微合夥人, 5%) <- 用戶4 (微合夥人, 5%)

當用戶4獲得1000 CV的推廣獎勵時：
- 總權重 = 0.05 + 0.10 + 0.15 = 0.30
- 用戶3獲得：1000 × (0.05/0.30) = 166.67 CV
- 用戶2獲得：1000 × (0.10/0.30) = 333.33 CV  
- 用戶1獲得：1000 × (0.15/0.30) = 500.00 CV

## 優點

1. **更公平的分配機制**：所有上線合夥人都有機會獲得獎勵
2. **鼓勵培養下線**：高等級合夥人有動機培養更多下線合夥人
3. **增加收益機會**：合夥人可以從多個下線獲得獎勵
4. **權重分配合理**：高等級合夥人獲得更多獎勵，符合階級制度

## 測試文件

- `tests/Unit/PartnerBonusDistributionTest.php` - 單元測試
- `demo_partner_bonus_distribution.php` - 功能演示腳本

## 注意事項

1. 最多向上查找10層，防止無限循環
2. 只有partner_bonus_ratio > 0的合夥人才參與分配
3. 如果沒有上線合夥人，則不進行分配
4. 保持原有的其他獎勵邏輯不變
