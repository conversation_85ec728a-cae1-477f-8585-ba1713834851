<?php

namespace App\Services\pattern;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Services\CommonService;
use App\Services\SoldCountService;
use App\Services\pattern\HelperService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\BonusHelper;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\Proposal as DiscountProposal;
use App\Services\pattern\recursiveCorrdination\discountRC\MemberFactory as DiscountMemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\ActCalculate;
use App\Services\pattern\simpleFactory\discountFactory\DiscountFactory;
use App\Http\Controllers\admin\Payfee;
use App\Http\Controllers\admin\CartMethod;
use Illuminate\Support\Facades\Log;

class OrderHelper
{
    const PER_PAGE_ROWS = 20;
    const SIMPLE_MODE_PAGINATE = false;

    /*取得對應商品前台的config db設定*/
    public static function get_shop_db_config($order_number)
    {
        return substr($order_number, 0, 1) . '_sub';
    }

    /*將商品加入購物車*/
    public static function cartCtrl(
        string $cart_key,
        int $num = 0,
        string $cmd = 'increase',
        string $cart_session = 'cart_all',
        int $user_id = 0
    ) {
        if (!empty(config('control.close_function_current')['訂單管理'])) {
            return [
                'code' => 0,
                'msg' => Lang::get('操作失敗'),
                'num' => $num,
            ];
        }

        /*輸入參數調整*/
        $num = $num ? $num : 0;
        $cmd = $cmd ? $cmd : 'increase';
        $cart_session = $cart_session ? $cart_session : 'cart_all';

        $Proposal = self::get_Proposal_GetCartData($cart_session, $user_id);
        $cart = $Proposal->getCartArray();
        // dump($cart);exit;

        [$type_id_ori, $key_type] = Proposal::get_prod_key_type($cart_key);
        $cart_key = $type_id_ori . '_' . $key_type; /*強制所有cart_key帶上key_type*/

        $code = 1;
        if ($cmd != 'delete') { /*刪除以外的操作才需檢查*/
            if (substr($key_type, 0, 3) == 'kol') { // 有掛某網紅
                $productinfo_type = DB::table('productinfo_type')->find($type_id_ori); // 找出該品項
                $productinfo_type = CommonService::objectToArray($productinfo_type);
                $productinfo = DB::table('productinfo')->find($productinfo_type['product_id']); // 找出該商品
                $prodcut = CommonService::objectToArray($productinfo);
                $kol_id = str_replace('kol', '', $key_type); // 取出網紅ID
                // 找出該商品的與該網紅關使用中的最新紀錄
                $kol_productinfo = DB::table('kol_productinfo')->where('productinfo_id =' . $productinfo_type['product_id'] . ' AND is_using=1 AND kol_id=' . $kol_id)->orderBy('id', 'desc')->get();

                if (!empty($kol_productinfo)) { // 檢查商品是該網紅代銷中
                    // 該網紅代銷中，接續檢查網紅是否已到開賣日
                    $kol = DB::table('kol_period')->where('date_start <="' . date('Y-m-d') . '" AND date_end >="' . date('Y-m-d') . '"')
                        ->where('kol_id', $kol_id)
                        ->first(); // 找出該網紅
                    $kol = CommonService::objectToArray($kol);
                    if (!$kol) { // 檢查網紅起賣日是否大於現在
                        // 未開賣
                        $key_type = 'normal';
                        $cart_key = $type_id_ori . '_normal';
                    }
                } else {
                    // 非該網紅代銷中
                    $key_type = 'normal';
                    $cart_key = $type_id_ori . '_normal';
                }
            }

            // 依加入購物車的品項區分檢查方式
            switch ($key_type) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($key_type, 0, 3) == 'kol':
                case 'add':
                case substr($key_type, 0, 8) == 'askprice':
                    /*檢查目標商品可加入購物車*/
                    $result = self::check_status_can_add_cart($num, $type_id_ori, $cmd, $key_type);
                    if ($result['code'] == 0) {
                        $code = 0;
                        $msg = $result['msg'];
                        break;
                    }
                    $productinfo_type = DB::table('productinfo_type')->find($type_id_ori); // 找出該品項
                    $productinfo_type = CommonService::objectToArray($productinfo_type);
                    $productinfo = DB::table('productinfo')->find($productinfo_type['product_id']); // 找出該商品
                    $productinfo = CommonService::objectToArray($productinfo);
                    /*如果目標購物車是cart，則要檢查賣家是否統一*/
                    $distributor_id = $cart_session == 'cart' ? $productinfo['distributor_id'] : '';

                    /*按種類整理購物車商品，統計數量*/
                    $store_num = []; // 有加入購物車的商品的庫存量(結果為扣除cart_key商品外的購買量)
                    $addprice_num = []; // 加購商品累積數量
                    foreach ($cart as $cart_key2 => $cart_v) { /*對每個購物車內的商品處理，計算庫存及加購商品累積數量*/
                        [$cart_key_id, $cart_key_type] = Proposal::get_prod_key_type($cart_key2);
                        if (!in_array($cart_key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
                            /*檢查目購物車內商品可加入購物車*/
                            $result = self::check_status_can_add_cart($num, $cart_key_id, $cmd, $cart_key_type, $distributor_id);
                            if ($result['code'] == 0) {
                                $code = 0;
                                $msg = $result['msg'];
                                break;
                            } else {
                                if (!isset($store_num[$cart_key_id])) {
                                    $store_num[$cart_key_id] = $result['target_p'];
                                }
                            }
                            if ($cart_key2 != $cart_key) { /*操作的商品非本次要加入購物車的商品*/
                                $store_num[$cart_key_id]['num'] -= $cart_v; /*從記錄的庫存中扣除該數量*/
                            }

                            if (substr($cart_key_type, 0, 3) == 'add') { /*統計購物車裡各品項加價購商品的數量*/
                                if (!isset($addprice_num[$cart_key_id])) {
                                    $addprice_num[$cart_key_id] = $cart_v;
                                } else {
                                    $addprice_num[$cart_key_id] += $cart_v;
                                }
                            }
                        }
                    }
                    if ($key_type == 'add') { /*檢查加價購商品數量上限*/
                        $addable_addprice = Proposal::get_addprice_products_by_cart();
                        $num_limit = false;
                        $addable_addprice_num = isset($addable_addprice['type' . $type_id_ori]) ? $addable_addprice['type' . $type_id_ori]['adp_p_num'] : 0; /*找出加入購物車的品項的可加購量*/
                        if (empty($addprice_num[$type_id_ori])) { // 購物車內無此商品
                            if ($addable_addprice_num < $num) { // 可加購量小於 本次加入的量
                                $num_limit = true;
                            }
                        } else if ($cmd == 'increase') { // 用加的方法
                            if ($addable_addprice_num < $addprice_num[$type_id_ori] + $num) { // 可加購量小於 購物車累積量 + 本次加入的量
                                $num_limit = true;
                            }
                        } else if ($cmd == 'assign') { // 用指派數量的方法
                            if ($addable_addprice_num < $addprice_num[$type_id_ori] - $cart[$cart_key] + $num) { // 可加購量小於 購物車累積的量 - 某品項的量 + 某品項指派的量
                                $num_limit = true;
                            }
                        }
                        if ($num_limit) {
                            $code = 0;
                            $msg = Lang::get('超出加價購數量上限') . '：' . $productinfo['title'];
                            break;
                        }
                    }

                    /*統計購買量*/
                    $buy_num = $num; /*品項單一類類型購買數量(區分一般、網紅推薦、加價購...)*/
                    $storeNum = isset($store_num[$type_id_ori]) ? $store_num[$type_id_ori]['num'] : DB::table('productinfo_type')->find($type_id_ori)->num;
                    if (!isset($store_num[$type_id_ori])) { // 購物車內無此商品
                    } else if ($cmd == 'increase') { // 用加的方法
                        if (isset($cart[$cart_key])) {
                            $buy_num = $cart[$cart_key] + $num;
                        } // 購買數量是 購物車量 + 本次加入的量
                    } else if ($cmd == 'assign') { // 用指派數量的方法
                    }

                    if (substr($key_type, 0, 8) == 'askprice') { /*檢查詢價商品數量是否相同*/
                        $askprice_id = str_replace('askprice', '', $key_type);
                        $AskpriceHelper = new AskpriceHelper();
                        $result = $AskpriceHelper->getOne_by_main_id($askprice_id, 'a.user_id ="' . $user_id . '"');
                        $main = $result['main'];
                        if (!$main) {
                            $code = 0;
                            $msg = Lang::get('資料有誤');
                            break;
                        }
                        $main_record = $result['current'];
                        if (!$main_record) {
                            $code = 0;
                            $msg = Lang::get('資料有誤');
                            break;
                        }
                        if ($main_record['status'] != 1) {
                            $code = 0;
                            $msg = Lang::get('此詢價尚未被回覆');
                            break;
                        }
                        if ($main_record['agree'] != 1) {
                            $code = 0;
                            $msg = Lang::get('此詢價未被同意');
                            break;
                        }
                        if ($main_record['expired_date']) {
                            if (strtotime($main_record['expired_date'] . ' +1Day') < time()) {
                                $code = 0;
                                $msg = Lang::get('已過購買期限');
                                break;
                            }
                        }
                        if ($main_record['bought'] == 1) {
                            $code = 0;
                            $msg = Lang::get('已完成此詢價的購買');
                            break;
                        }
                        if ($main_record['num'] != $buy_num) {
                            $code = 0;
                            $msg = Lang::get('不可修改詢價購買的數量');
                            break;
                        }
                    }

                    /*檢查購物車商品庫存*/
                    if (empty(config('control.close_function_current')['庫存警示'])) { /*檢查此商品的庫存*/
                        $num_limit = false;
                        if ($storeNum < $buy_num) { // 庫存若小於購買數量
                            $num_limit = true;
                        }
                        if ($num_limit) {
                            if (config('control.control_pre_buy') == 0 || $productinfo['pre_buy'] == 0) {
                                $productinfo_and_productinfo_type = $productinfo['title'];
                                if ($productinfo_type['title']) {
                                    $productinfo_and_productinfo_type .= ' - ' . $productinfo_type['title'];
                                }
                                $code = 0;
                                $msg = Lang::get('庫存不足') . '：' . $productinfo_and_productinfo_type;
                                break;
                            } else {
                                if (self::buy_cur_and_pre_at_same_time($cart_key, $buy_num, $user_id)) { /*檢查同一品項的類型商品是否同時買現貨及超額購買*/
                                    $productinfo_and_productinfo_type = $productinfo['title'];
                                    if ($productinfo_type['title']) {
                                        $productinfo_and_productinfo_type .= ' - ' . $productinfo_type['title'];
                                    }
                                    $code = 0;
                                    $msg = Lang::get('無法同時購買現貨及預購') . '：' . $productinfo_and_productinfo_type;
                                    break;
                                }

                                if ($productinfo['pre_buy_limit'] > 0) {
                                    $ori_pre_buy_num = 0;
                                    $pre_buy_product = DB::connection('main_db')->table('orderform_product AS op')
                                        ->select('op.*')
                                        ->join('orderform AS o', 'o.id', 'op.orderform_id')
                                        ->whereRaw('o.status="New"')
                                        ->where('op.pre_buy', 1)
                                        ->where('op.type_id', $type_id_ori)
                                        ->get();
                                    $pre_buy_product = CommonService::objectToArray($pre_buy_product);
                                    foreach ($pre_buy_product as $product_v) {
                                        $ori_pre_buy_num += $product_v['pre_buy_num'] ?? 0;
                                    }
                                    $pre_buy_rest = $productinfo['pre_buy_limit'] - $ori_pre_buy_num;
                                    if ($pre_buy_rest - $buy_num < 0) {
                                        $code = 0;
                                        $msg = Lang::get('超過超額購買總上限') . ':' . $productinfo['title'] . '(' . Lang::get('剩餘') . '：' . $pre_buy_rest . ')';
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    $MemberInstance = new MemberInstance($user_id);
                    $member_data = $MemberInstance->get_user_data();
                    /*檢查課程需求等級*/
                    if ($productinfo['vip_type_require']) { /*有設定需求*/
                        $arr_vip_types = MemberInstance::get_vip_types([], true)['db_data'];

                        $type_key_rank = array_keys($arr_vip_types);
                        $cur_rank = array_search($member_data['vip_type_course'], $type_key_rank);
                        $cur_rank = $cur_rank !== false ? $cur_rank : -1;
                        $need_rank = array_search($productinfo['vip_type_require'], $type_key_rank);
                        $need_rank = $need_rank !== false ? $need_rank : -1;
                        if ($cur_rank < $need_rank) {
                            $code = 0;
                            $msg = Lang::get('未達商品要求課程進度等級') . ':' . $arr_vip_types[$productinfo['vip_type_require']]['vip_name'];
                            break;
                        }
                    }
                    //檢查商品是否小於最低
                    if ($productinfo['product_cate'] == 1) { /*式投資商品*/
                        if ($member_data['partner_level_id'] == 0) {/*會員尚無合夥等級*/
                            $partner_levels = MemberInstance::get_partner_levels([])['db_data'];
                            $partner_level = $partner_levels[0];
                            if ($productinfo_type['price_cv'] < $partner_level['contribution']) { /*品項CV金額小於最低合夥人階級*/
                                $code = 0;
                                $msg = Lang::get('請先購買直接升級的合夥方案');
                                break;
                            }
                        }
                    }
                    break;

                case 'coupon': // 優惠券商品
                    $respData = ProductHelpler::checkCouponNum($type_id_ori, $user_id, $num, $cmd);
                    if ($respData['code'] == 0) {
                        $code = 0;
                        $msg = $respData['msg'];
                        break;
                    }
                    break;

                default:
                    break;
            }
        }
        if ($code == 0) {
            return [
                'code' => $code,
                'msg' => $msg,
                'num' => $num,
            ];
        }

        // 操作(加減)購物車
        try {
            self::set_cart_session($cart_session, $cmd, $cart_key, $num, $user_id);
            /*沒有啟用平台 且 操作刪除*/
            if (config('control.control_platform') != 1 && $cmd == 'delete') {
                self::set_cart_session('cart_all', $cmd, $cart_key, $num, $user_id);
            }
            $Proposal = self::get_Proposal_GetCartData($cart_session, $user_id);
            return [
                'code' => 1,
                'msg' => sizeof($Proposal->getCartArray()),
                'num' => $num,
            ];
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'num' => $num,
            ];
        }
    }

    /*檢查是否可有同時購買現貨及超額購買*/
    public static function buy_cur_and_pre_at_same_time($cart_key, $buy_num, $user_id)
    {
        $type_id = explode('_', $cart_key)[0];
        $stock = DB::table('productinfo_type')->find($type_id);
        $stock = CommonService::objectToArray($stock);
        if (!$stock) {
            return true;
        }             /*找無品項*/
        if ($stock['num'] <= 0) {
            return false;
        }   /*庫存已小於零，不可能同時買現貨及超額購買*/

        $same_type_product_num = [];    /*同品項的各類型商品在購物車中的數量(區分一般、網紅、加價購...)*/
        $Proposal = self::get_Proposal_GetCartData('cart', $user_id);
        $cart = $Proposal->getCartArray();
        foreach ($cart as $key => $value) {
            if ($key == $cart_key) {  /*如果是操作中的品項*/
                $num = $buy_num;    /*依修正數量*/
            } else if (preg_match('/' . $type_id . '_/', $key)) {
                $num = $value;      /*依購物車紀錄*/
            } else {
                continue;           /*略過不同品項的商品*/
            }
            array_push($same_type_product_num, $num);
        }
        // dump($same_type_product_num);
        $elements = CommonService::elements_able_to_sum_to_target($same_type_product_num, $stock['num']); /*是否能剛好組成等於庫存數量*/
        return count($elements) == 0; /*如果回傳陣列長度為0，表示組不出剛好能等於庫存的量，代表同時購買現貨及超額購買*/
    }

    /*建立訂單*/
    public static function createOrder($OrderData, $buy_method = 'online', $userId = 0)
    {
        $OrderData['become_member'] = $OrderData['become_member'] ?? '0';

        $MemberInstance = new MemberInstance($userId);
        $member_data = $MemberInstance->get_user_data($addr_change = "ori", ['a.id' => $userId]);

        //get cart data
        $Proposal = self::get_Proposal_GetCartData('cart', $userId);

        $cartData = [];
        foreach ($Proposal->getCartArray() as $key => $value) {
            $singleData = Proposal::get_singleData($key, $buy_method); /* 取得商品資料 */
            $singleData['num'] = $value;
            array_push($cartData, $singleData);
        }
        //----

        //get discount data
        $DiscountProposal = DiscountProposal::withTeamMembersAndRequire(
            ['Total', 'CouponCheck', 'PointCheck', 'ActCalculate', 'MemberDiscount', 'ContributionDeduct'],
            [
                'user_id' => $userId,
                'cartData' => $cartData,
            ]
        );
        $DiscountProposal = DiscountMemberFactory::createNextMember($DiscountProposal);
        //----

        /*檢查圓滿點數抵扣*/
        $ContributionDeduct = $DiscountProposal->projectData['ContributionDeduct'];
        $sum_deduct_invest = 0; /*功德圓滿點數總用量*/
        $sum_deduct_consumption = 0; /*消費圓滿點數總用量*/
        $contribution_deduct = json_decode($OrderData['contribution_deduct'], true);
        foreach ($contribution_deduct as $cart_key => $deduct_set) {
            $deduct_invest = (int)($deduct_set['deduct_invest'] ?? 0);
            $deduct_consumption = (int)($deduct_set['deduct_consumption'] ?? 0);
            $cv_deduct_limit = $ContributionDeduct['cartData_deduct'][$cart_key] ?? 0;
            $cv_deduct_limit_consumption = $ContributionDeduct['cartData_deduct_consumption'][$cart_key] ?? 0;

            $sum_deduct_invest += $deduct_invest;
            $sum_deduct_consumption += $deduct_consumption;
            if ($deduct_consumption > $cv_deduct_limit_consumption) {
                throw new \Exception(Lang::get('超過消費圓滿點數使用上限') . ":" . floor($cv_deduct_limit_consumption));
            } else if ($deduct_invest + $deduct_consumption > $cv_deduct_limit) {
                throw new \Exception($deduct_set['info_title'] . Lang::get('超出折抵上限') . ":" . floor($cv_deduct_limit));
            } else if ($sum_deduct_invest > $ContributionDeduct['increasing_limit_invest']) {
                throw new \Exception(Lang::get('您僅擁有功德圓滿點數') . ":" . floor($ContributionDeduct['increasing_limit_invest']));
            } else if ($sum_deduct_consumption > $ContributionDeduct['increasing_limit_consumption']) {
                throw new \Exception(Lang::get('您僅擁有消費圓滿點數') . ":" . floor($ContributionDeduct['increasing_limit_consumption']));
            }
        }
        //----

        $user_collection = Db::connection('main_db')->table('account')->get();
        $db_id_to_user = $user_collection->keyBy('id')->toArray();
        //create products data
        $need_shipfee = false;  // 是否需運費
        $products = [];
        $buy_coupon = [];       // 購買優惠券(成單後派發優惠券用)
        $freeDiscountSum = 0;   // 立馬省總優惠金額
        $pre_buy_num_group = []; // 依照品項回傳購物車中超額購買的商品數量組合
        foreach ($cartData as $singleData) {
            if (in_array($singleData['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*不屬於 productinfo商品*/
                continue; /*略過可超額購買數量統計*/
            }
            $pre_buy_num_group[$singleData['type_id_ori']] = self::get_pre_buy_num_group($singleData['type_id'], $singleData['num'], $userId); /*取得超額購買數組*/
        }
        // dump($pre_buy_num_group);
        // dump($cartData);
        foreach ($cartData as $singleData) {
            if (!in_array($singleData['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
                $need_shipfee = true; // 需計算運費
            }

            $url2 = $singleData['info_pic1'];
            $product_name = $singleData['type_title'] ? $singleData['info_title'] . '-' . $singleData['type_title'] : $singleData['info_title'];
            $product = [
                'name' => $product_name,                                                // 商品名
                'price' => $singleData['countPrice'],                                   // 單價
                'num' => $singleData['num'],                                            // 數量
                'total' => $singleData['countPrice'] * $singleData['num'],              // 小計
                'url2' => request()->server('HTTP_HOST') . '/public/static/index' . $url2,  // 圖片網址
                'type_id' => $singleData['type_id_ori'],                                // 商品品項id
                'info_id' => $singleData['type_product_id'],                            // 商品id
                'key_type' => $singleData['key_type'],                                  // 商品種類(ex:一般商品、優惠券商品...)
            ];

            // 根據商品商品種類產生對應資料
            switch ($singleData['key_type']) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($singleData['key_type'], 0, 3) == 'kol':
                case 'add':
                case substr($singleData['key_type'], 0, 8) == 'askprice':
                    // 因為立馬省改動過單價，這邊要記錄回來真實的單價
                    if (
                        $singleData['key_type'] == 'normal'
                        || substr($singleData['key_type'], 0, 3) == 'kol'
                    ) { /*一般商品 或 網紅商品 需檢查立馬省*/
                        $productinfo_type = [];
                        $productinfo_type = DB::table('productinfo_type')->find($singleData['type_id_ori']);
                        $productinfo_type = CommonService::objectToArray($productinfo_type);
                        if ($productinfo_type) {
                            $origin_price = $productinfo_type['count'];
                            $product['price'] = $origin_price;
                            $product['total'] = $origin_price * $singleData['num'];
                        }

                        // 計算立馬省總優惠金額
                        $act_type_sql = ActCalculate::arrange_search_sql($act_type = 2);
                        $actProductInList = DB::table('act_product')
                            ->select('act.*')
                            ->join('act', 'act.id', '=', 'act_product.act_id')
                            ->whereRaw('act_product.prod_id = "' . $productinfo_type['product_id'] . '"')
                            ->whereRaw($act_type_sql)
                            ->get();
                        $actProductInList = CommonService::objectToArray($actProductInList);
                        if (count($actProductInList) > 0 && $singleData['key_type'] != 'add') { /*有套用利馬省 且 非加價購商品*/
                            if ($actProductInList[0]['discount1'] > $origin_price) { /*立馬省折扣大於商品金額*/
                                $freeDiscountSum += $singleData["num"] * $origin_price;
                            } else {
                                $freeDiscountSum += $singleData["num"] * $actProductInList[0]['discount1'];
                            }
                        }
                    }

                    $up = DB::table('productinfo')->find($singleData['type_product_id']);
                    $up = CommonService::objectToArray($up);
                    $position =  DB::table('productinfo_type as pt')
                        ->select('pos.name')
                        ->join('position as pos', 'pt.position', '=', 'pos.id')
                        ->where('pt.id', $singleData['type_id_ori'])->first()->name;
                    $product['Author'] = $up['Author'];
                    $product['house'] = $up['house'];
                    $product['house_date'] = $up['house_date'];
                    $product['ISBN'] = $up['ISBN'];
                    $product['position'] = $position;
                    $product['url'] = request()->server('HTTP_HOST') . '/admin/productinfo/edit?id=' . $singleData['type_product_id'];
                    $product['info_id'] = $singleData['type_product_id'];
                    $product['is_registrable'] = $singleData['is_registrable'];
                    $product['deal_position'] = $buy_method == 'online' ? "0" : "1"; // 線上購物需核銷庫存
                    $product['pre_buy'] = 0;
                    $product['pre_buy_num'] = 0;
                    $product['product_cate'] = $singleData['product_cate'];
                    if ($product['product_cate'] == 1) { /*投資*/
                        $product['bonus_model_id'] = 0;
                        $product['use_ad'] = 0;
                    } else {
                        $product['bonus_model_id'] = $singleData['bonus_model_id'];
                        $product['use_ad'] = $singleData['use_ad'];
                    }
                    $product['vip_type_reward'] = $singleData['vip_type_reward'];
                    $product['vip_type_require'] = $singleData['vip_type_require'];
                    $product['deduct_invest'] = (int)($contribution_deduct[$singleData['type_id']]['deduct_invest'] ?? 0); /*使用功德圓滿點數*/
                    $product['deduct_consumption'] = (int)($contribution_deduct[$singleData['type_id']]['deduct_consumption'] ?? 0); /*使用消費圓滿點數*/
                    // 移除千分位逗號並轉換為數字
                    $clean_price_cv = self::cleanNumericValue($singleData['price_cv']);
                    $product['price_cv'] = $clean_price_cv * $singleData['num'];
                    $product['distributor_id'] = $singleData['distributor_id'];
                    if ($product['distributor_id'] == 0) { /*未設定供應商*/
                        $product['price_supplier'] = 0;
                        $product['supplier_bonus'] = 1;
                    } else {
                        // 移除千分位逗號並轉換為數字
                        $clean_price_supplier = self::cleanNumericValue($singleData['price_supplier']);
                        $product['price_supplier'] = $clean_price_supplier * $singleData['num'];
                        $product['supplier_bonus'] = $db_id_to_user[$product['distributor_id']]->supplier_bonus ?? 1;
                    }
                    $in_array_index = array_search($singleData['num'], $pre_buy_num_group[$singleData['type_id_ori']] ?? []);
                    if ($in_array_index !== false) { /*是組成超額購買的數量*/
                        $product['pre_buy'] = 1;
                        $product['pre_buy_num'] = $singleData['num'];
                        array_splice($pre_buy_num_group[$singleData['type_id_ori']], $in_array_index, 1);
                    }
                    break;

                case 'coupon': // 優惠券商品
                    $product['Author'] = '';
                    $product['house'] = '';
                    $product['ISBN'] = '';
                    $product['position'] = '無';
                    $product['url'] = request()->server('HTTP_HOST') . '/Coupon/show?id=' . $singleData['type_product_id'];
                    $product['info_id'] = "0";
                    $product['is_registrable'] = "0";
                    $product['deal_position'] = "1"; // 不需銷庫存
                    $product['pre_buy'] = 0;
                    $product['pre_buy_num'] = 0;
                    $product['product_cate'] = 0;
                    $product['bonus_model_id'] = 0;
                    $product['use_ad'] = 0;
                    $product['vip_type_reward'] = 0;
                    $product['vip_type_require'] = 0;
                    $product['deduct_invest'] = 0;
                    $product['deduct_consumption'] = 0;
                    $product['price_cv'] = 0;
                    $product['distributor_id'] = 0;
                    $product['price_supplier'] = 0;
                    $product['supplier_bonus'] = 1;
                    $buy_coupon[$singleData['type_product_id']] = $singleData['num'];
                    break;
            }
            array_push($products, $product);
        }
        // dump($freeDiscountSum);
        // dump($products);exit;
        if (count($products) == 0) {
            throw new \Exception(Lang::get('購物車內無商品'));
        }
        //----

        // 處理運費
        $shipping = self::get_shipping_method($cartData, $OrderData['send_way']);
        // dump($shipping);exit;
        if ($OrderData['send_way'] == Lang::get('到店取貨') || !$need_shipfee) { /*商品銷售、到店取貨、不需運費 使用*/
            array_push($products, [
                'name' => '運費',
                'price' => 0,
                'num' => 1,
                'total' => 0
            ]);
            $shipping[0]['price'] = 0;
        } else { // 有需運送的商品，且配送方式非到店取貨
            if (!$shipping) {
                throw new \Exception(Lang::get('無此運送方式'));
            }
            $OrderData['send_way'] = $shipping[0]['name'];

            if ($DiscountProposal->projectData['Total'] > $shipping[0]['free_rule'] && $shipping[0]['free_rule'] > 0) { // 如果購物金額超過免運條件
                $shipping[0]['price'] = 0; // 設定運費為0元
            }

            array_push($products, [
                'name' => $shipping[0]['name'],
                'price' => $shipping[0]['price'],
                'num' => 1,
                'total' => $shipping[0]['price']
            ]);
        }
        //----

        //create add_point data
        $add_point = 0;
        // if( empty(config('control.close_function_current')['點數設定']) ){
        //     if($OrderData['pay_way'] != 4){
        //         if($OrderData['discount'] == 'none_discount'){
        //             $point_rate = (float)DB::table('points_setting')->find(3)->value;
        //             $add_point = floor(($DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['acts']['sumNoneGetPoint']) / $point_rate);
        //         }
        //     }
        // }
        //----

        //產生優惠說明 & 計算購物總金額
        // dump($OrderData['discount']);exit;
        if (!isset($OrderData['discount'])) {
            throw new \Exception(Lang::get('發生錯誤，請再試一次'));
        }
        $discountData = explode("_", $OrderData['discount']);
        if (count($discountData) < 2) {
            throw new \Exception(Lang::get('發生錯誤，請再試一次'));
        }
        if ($discountData[0] == 'points') {
            $discountData[1] = $OrderData['point'];
        }

        if ($discountData[0] == 'acts') { // 活動優惠
            $discountFinal = [];
            foreach ($DiscountProposal->projectData['acts']['actCart'] as $acKey => $acValue) {
                $actDiscount = $acValue['calculated']['discount'];
                if (($acValue['type'] == 1) | ($acValue['type'] == 3)) {
                    $discountFinal[] = [
                        'type' => Lang::get('活動'),
                        'name' => $acValue['name'],
                        'count' => Lang::get('打') . $actDiscount . Lang::get('折'),
                        'product' => $acValue['prod']
                    ];
                } else {
                    $discountFinal[] = [
                        'type' => Lang::get('活動'),
                        'name' => $acValue['name'],
                        'count' => Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $actDiscount,
                        'product' => $acValue['prod']
                    ];
                }
            }
            $discountFinalData['discount'] = json_encode($discountFinal, JSON_UNESCAPED_UNICODE);
            $discountFinalData['total'] = $DiscountProposal->projectData['acts']['sum'];
        } else if ($discountData[0] == "firstbuy") { // 會員首購優惠
            $discountFinalData['discount'] = urldecode(json_encode([
                [
                    'type' => urlencode(Lang::get('會員首購優惠')),
                    'name' => urlencode($DiscountProposal->projectData['firstBuyDiscount']['vip_name']),
                    'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $DiscountProposal->projectData['firstBuyDiscount']['discount']),
                ]
            ]));
            $discountFinalData['total'] = $DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['firstBuyDiscount']['discount'];
        } else if ($discountData[0] == "vipdiscount") { // VIP會員優惠
            $discountFinalData['discount'] = urldecode(json_encode([
                [
                    'type' => urlencode(Lang::get('會員等級優惠')),
                    'name' => urlencode($DiscountProposal->projectData['vipDiscount']['vip_name']),
                    'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $DiscountProposal->projectData['vipDiscount']['discount']),
                ]
            ]));
            $discountFinalData['total'] = $DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['vipDiscount']['discount'];
        } else { // 其他優惠(ex:紅利、優惠券、直接輸入型優惠券、無優惠)
            $discountObject = DiscountFactory::createDiscount(
                $discountData,
                $DiscountProposal->projectData['Total']
            );
            $discountFinalData = $discountObject->getDiscountAndTotal($OrderData);
            $discountFinalData['total'] = $discountFinalData['total'] < 0 ? 0 : $discountFinalData['total'];
        }
        //----

        //---- count free(auto) discount
        $freeDiscount = $buy_method == 'online' ? $freeDiscountSum : 0;
        // dump($freeDiscount);
        //----------------

        $discountFinalData['total'] = $discountFinalData['total'] < 0 ? 0 : $discountFinalData['total'];
        $discountFinalData['total'] += $shipping[0]['price'];
        // dump($discountFinalData);exit;

        if ($userId) {
            $role = 'member';
        } else {
            $role = 'guest';
        }

        $receipts_state = isset($OrderData['receipts_state']) ? $OrderData['receipts_state'] : '0';
        $transport_state = isset($OrderData['transport_state']) ? $OrderData['transport_state'] : '0';
        $order_status = isset($OrderData['status']) ? $OrderData['status'] : 'New';

        // dump($sum_deduct_invest);
        // dump($sum_deduct_consumption);
        // dump($products);exit;
        $total = round($discountFinalData['total']) - (($sum_deduct_invest + $sum_deduct_consumption) * config('extra.skychakra.exchange_rate'));
        if ($total < 0) {
            throw new \Exception(Lang::get('訂單金額不可小於0'));
        }
        $insertData = [
            'distributor_id' => isset($OrderData['distributor_id']) ? $OrderData['distributor_id'] : 0,
            'user_id' => $userId,
            'order_number' => self::create_order_number(),
            'create_time' => time(),
            'over_time' => self::get_eff_dateline(),
            'payment' => htmlspecialchars(trim($OrderData['pay_way'])),
            'transport' => htmlspecialchars(trim($OrderData['send_way'])),
            'transport_location_name' => htmlspecialchars(trim($OrderData['transport_location_name'])),
            'transport_location_phone' => htmlspecialchars(trim($OrderData['transport_location_phone'])),
            'transport_location_tele' => '',
            'transport_location_textarea' => '',
            'transport_email' => htmlspecialchars(trim($OrderData['transport_email'])),
            'product' => json_encode($products, JSON_UNESCAPED_UNICODE),
            'add_point' => $add_point,
            'total' => (int)$total,
            'contribution_deduct' => $sum_deduct_invest + $sum_deduct_consumption,
            'discount' => $discountFinalData['discount'],
            'freediscount' => $freeDiscount,
            'receipts_state' => $receipts_state,
            'transport_state' => $transport_state,
            'uniform_numbers' => htmlspecialchars(trim($OrderData['uniform_numbers'])),
            'company_title' => htmlspecialchars(trim($OrderData['company_title'])),
            'ps' => htmlspecialchars(trim($OrderData['ps'])),
            'status' => $order_status,
            'InvoiceStyle' => intval($OrderData['invoice_style']),

            /*綠界物流資料*/
            'MerchantTradeNo' => $OrderData['MerchantTradeNo'] ?? '',
            'LogisticsSubType' => $OrderData['LogisticsSubType'] ?? '',
            'CVSStoreID' => $OrderData['CVSStoreID'] ?? '',
            'CVSStoreName' => $OrderData['CVSStoreName'] ?? '',
            'CVSAddress' => $OrderData['CVSAddress'] ?? '',
            'CVSTelephone' => $OrderData['CVSTelephone'] ?? '',
            'CVSOutSide' => $OrderData['CVSOutSide'] ?? '',
            'ExtraData' => $OrderData['ExtraData'] ?? '',
        ];

        if ($insertData['payment'] == 2 && $insertData['total'] == 0 && $insertData['total'] <= 15) {
            if ($discountData[0] == 'points') {
                $input['msg'] = '回補' . $DiscountProposal->projectData['Total'] . '點(訂單錯誤)';
                $input['points'] = $DiscountProposal->projectData['Total'];
                $input['user_id'] = $userId;
                $input['belongs_time'] = time();
                $input['msg_time'] = date('Y-m-d H:i:s');
                Db::connection('main_db')->table('points_record')->insert($input);
                Db::connection('main_db')->table('account')->where('id', $userId)->increment('point', $DiscountProposal->projectData['Total']);
            }
            throw new \Exception(Lang::get('匯款訂單金額需大於15元'));
        }

        if ($insertData['total'] == 0) {
            $insertData['receipts_state'] = 1;
        }

        // 物流資訊
        if (empty($OrderData['addrC']) == false) {
            $insertData['transport_location'] = $OrderData['addrC'];
        } else {
            if (empty($OrderData['transport_location']) == false) {
                $insertData['transport_location'] = $OrderData['transport_location'];
            } else {
                $insertData['transport_location'] = '無';
            }
        }

        /* 發票設定 */
        switch ($insertData['InvoiceStyle']) {
            default:
                $insertData['Print'] = 1;
                break;
            case 3:
                $insertData['CarrierType'] = trim($OrderData['CarrierType']);
                $insertData['CarrierNum'] = trim($OrderData['CarrierNum']);
                $insertData['Print'] = 0;
                break;
            case 4:
                $insertData['uniform_numbers'] = trim($OrderData['uniform_numbers']);
                $insertData['company_title'] = trim($OrderData['company_title']);
                $insertData['Print'] = 1;
                break;
            case 5:
                $insertData['LoveCode'] = trim($OrderData['LoveCode']);
                $insertData['Print'] = 0;
        }

        //資料庫新增訂單
        // dump($OrderData);
        // dump($insertData);exit;
        $id = DB::connection('main_db')->table('orderform')->insertGetId($insertData);

        /*把商品插入 orderform_product 中*/
        $product_keys = array_keys($products[0] ?? []);
        $products_all = [];
        foreach ($products as $key => $value) {
            foreach ($product_keys  as $product_key) {
                $products_all[$key][$product_key] = $value[$product_key] ?? null;
            }
            $products_all[$key]['orderform_id'] = $id;
        }
        // dump($products_all);exit;
        DB::connection('main_db')->table('orderform_product')->insert($products_all);

        // 添加圓滿點數使用紀錄
        if ($sum_deduct_invest) { /*功德圓滿點數*/
            $MemberInstance->add_increasing_limit_record(
                $sum_deduct_invest * -1,
                '功德圓滿點數折抵(' . $insertData['order_number'] . ')',
                1,
                4
            );
        }
        if ($sum_deduct_consumption) { /*消費圓滿點數*/
            $MemberInstance->add_increasing_limit_record(
                $sum_deduct_consumption * -1,
                '消費圓滿點數折抵(' . $insertData['order_number'] . ')',
                2,
                4
            );
        }

        /*更新會員資料(建立會員訂單來源)*/
        $OrderData['become_member'] = $OrderData['become_member'] ?? '0';
        if ($OrderData['become_member'] == '1') {
            $become_member = $MemberInstance->get_user_data($addr_change = "ori", ['a.' . MemberInstance::$account_column => $insertData[MemberInstance::$account_column_order]]);
            if ($become_member) {
                if ($become_member['from_order'] == null) {
                    $MemberInstance->change_user_id($become_member['id']);
                    $MemberInstance->update_user_data([
                        'from_order' => $id,
                    ]);
                    Db::connection('main_db')->table('orderform')->where('id', $id)->update(['user_id' => $become_member['id']]);
                }
                if ($become_member['pwd'] == md5('1234')) {
                    session()->put('user', $member_data);
                }
            }
        }

        //建立直接輸入型優惠券使用紀錄
        if ($discountData[0] == 'directcoupon') {
            $discount = json_decode($insertData['discount'])[0];
            $discount->count = (int)mb_substr($discount->count, 2, -1);

            DB::table('coupon_direct_record')->insertGetId([
                'user_id'   => $userId,
                'coupon_id' => $discount->coupon_id,
                'order_id'  => $id,
                'datetime'  => $insertData['create_time'],
                'total'     => $insertData['total'],
                'discount'  => $discount->count,
            ]);
        }

        //更新購買優惠券
        if ($buy_coupon) {
            foreach ($buy_coupon as $key => $value) {
                DB::table('coupon_pool')->where('owner is null and login_time is null and use_time is null and coupon_id =' . $key)
                    ->limit($value)
                    ->update(['owner' => $userId]);
            }
        }

        if (empty(config('control.close_function_current')['消費抽抽樂'])) {
            // 處理抽抽樂優惠
            $MemberInstance = new MemberInstance($userId);
            $MemberInstance->set_lucky_draw($insertData['total'], $pay_record_id = 0, $id);
        }

        /*寄送提醒*/
        $globalMailData = HelperService::getMailData();
        $new_order_letter = Lang::get('訂單成功信消費者');
        $new_order_letter = str_replace("{globalMailData_mailFromName}", $globalMailData['mailFromName'], $new_order_letter);

        $res_goods = [];
        foreach ($products as $key => $value) {
            if (isset($value['key_type'])) {
                $res_good = $value['name'] . "*" . $value['num'];
                array_push($res_goods, $res_good);
            }
        }
        $res_goods = implode(', ', $res_goods);

        $buyer_name = $insertData['transport_location_name'];
        if (isset($member_data['name'])) {
            if ($member_data['name']) {
                $buyer_name = $member_data['name'];
            }
        }

        $payment_name = Payfee::get_payment_name($insertData['payment']);

        $mailBody = "
                <html>
                    <head></head>
                    <body>
                        <div>
                            " . $new_order_letter . "
                            " . Lang::get('訂單編號') . "：" . $insertData['order_number'] . "<br>
                            " . Lang::get('訂單時間') . "：" . date('Y/m/d H:i', $insertData['create_time']) . "<br>
                            " . Lang::get('訂購商品') . "：" . $res_goods . "<br>
                            " . Lang::get('訂單金額') . "：" . $insertData['total'] . "<br>
                            " . Lang::get('購買人') . "：" . $buyer_name . "<br>
                            " . Lang::get('收件人') . "：" . $insertData['transport_location_name'] . "<br>
                            " . Lang::get('出貨地址') . "：" . $insertData['transport_location'] . "<br>
                            " . Lang::get('電子信箱') . "：" . $insertData['transport_email'] . "<br>
                            " . Lang::get('行動電話') . "：" . $insertData['transport_location_phone'] . "<br>
                            " . Lang::get('聯絡電話') . "：" . $insertData['transport_location_tele'] . "<br>
                            " . Lang::get('付款方式') . "：" . $payment_name . "<br>
                            " . Lang::get('備註') . "：" . $insertData['transport_location_textarea'] . "<br>
                        </div>
                        <div>
                        " . $globalMailData['system_email']['order_complete'] . "
                        </div>
                        <div style='color:red;'>
                            ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . "(" . $id . ") ≡
                        </div>
                    </body>
                </html>
            ";
        $mail_return = HelperService::Mail_Send($mailBody, 'client', $insertData['transport_email'], Lang::get('訂單建立成功'));

        $new_order_letter_admin = Lang::get('menu.訂單成功信管理者');
        $mailBody = "
                <html>
                    <head></head>
                    <body>
                        <div>
                            " . $new_order_letter_admin . "
                            " . Lang::get('訂單編號') . "：" . $insertData['order_number'] . "<br>
                            " . Lang::get('訂單時間') . "：" . date('Y/m/d H:i', $insertData['create_time']) . "<br>
                            " . Lang::get('訂購商品') . "：" . $res_goods . "<br>
                            " . Lang::get('訂單金額') . "：" . $insertData['total'] . "<br>
                            " . Lang::get('購買人') . "：" . $buyer_name . "<br>
                            " . Lang::get('收件人') . "：" . $insertData['transport_location_name'] . "<br>
                            " . Lang::get('出貨地址') . "：" . $insertData['transport_location'] . "<br>
                            " . Lang::get('電子信箱') . "：" . $insertData['transport_email'] . "<br>
                            " . Lang::get('行動電話') . "：" . $insertData['transport_location_phone'] . "<br>
                            " . Lang::get('聯絡電話') . "：" . $insertData['transport_location_tele'] . "<br>
                            " . Lang::get('付款方式') . "：" . $payment_name . "<br>
                            " . Lang::get('備註') . "：" . $insertData['transport_location_textarea'] . "<br>
                        </div>
                        <div style='color:red;'>
                            ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . "(" . $id . ") ≡
                        </div>
                    </body>
                </html>
            ";
        $distributor_id = $insertData['distributor_id'];
        if ($distributor_id == 0) {
            $mail_return = HelperService::Mail_Send($mailBody, 'admin', '', Lang::get('新訂單提醒'));
        } else {
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();
            // dump($user_data);exit;
            if ($user_data) {
                $mail_return = HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('新訂單提醒'));
            }
        }

        // 更新商品已售出數量
        SoldCountService::updateSoldCountsForOrder($id);

        // 清空購物車
        self::clearCart($userId);

        return [
            'id' => $id,
            'total' => $insertData['total'],
            'order_number' => $insertData['order_number'],
            'role' => $role,
            'product' => $products,
            'pay_way' => trim($OrderData['pay_way']),
            'email' => trim($OrderData['transport_email']),
            'become_member'  => $become_member ?? null,
        ];
    }

    /**
     * 匯入訂單
     */
    public static function importOrder(array $arr_OrderData)
    {
        $user_collection = Db::connection('main_db')->table('account')->get();
        $db_number_to_user = $user_collection->keyBy('number')->toArray(); // 將會員編號轉為id的對應關係

        $currency_to_site = config('extra.skychakra.currency_to_site');

        $arr_vip_types = [];
        foreach (MemberInstance::get_vip_types([], true)['db_data'] as $key => $value) {
            $arr_vip_types[$value['vip_name']] = $value;
        }
        $arr_bonus_models = [];
        foreach (BonusSettingHelper::get_bonus_models([], true)['db_data'] as $key => $value) {
            $arr_bonus_models[$value['name']] = $value;
        }
        $arr_product_cate = [];
        foreach (BonusSettingHelper::get_product_cate([], true)['db_data'] as $key => $value) {
            $arr_product_cate[$value['name']] = $value;
        }
        $arr_use_ad = [];
        foreach (BonusSettingHelper::get_use_ad([], true)['db_data'] as $key => $value) {
            $arr_use_ad[$value['name']] = $value;
        }
        // dump($arr_vip_types);dump($arr_bonus_models);dump($arr_product_cate);dump($arr_use_ad);exit;

        $save_data = [];
        $array_error_data = [];

        foreach ($arr_OrderData as $data) {
            $data_ori = json_decode(json_encode($data), true);
            /*訂單資料*/
            if (($data['create_time'] ?? '') == '') { /*建立時間*/
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定訂單時間']);
                continue;
            } else {
                $timestamp = strtotime($data['create_time']);
                if ($timestamp === false || $timestamp <= 0) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '訂單時間格式錯誤：' . $data['create_time']]);
                    continue;
                }
                $data['create_time'] = $timestamp;
            }
            if ($data['user_id'] ?? '') { /*消費者編號*/
                if (!isset($db_number_to_user[$data['user_id']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此消費者']);
                    continue;
                } else {
                    $data['user_id'] = $db_number_to_user[$data['user_id']]->id;
                }
            } else {
                $data['user_id'] = 0;
            }
            if (($data['transport_location_name'] ?? '') == '') { /* 消費者姓名 */
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定消費者姓名']);
                continue;
            }
            if (($data['transport_location_phone'] ?? '') == '') {
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定消費者手機']);
                continue;
            }

            if ($data['user_id_marketing_dept'] ?? '') { /*行政廣告部門(會員編號)*/
                if (!isset($db_number_to_user[$data['user_id_marketing_dept']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此行政廣告部門']);
                    continue;
                } else {
                    $data['user_id_marketing_dept'] = $db_number_to_user[$data['user_id_marketing_dept']]->id;
                }
            } else {
                $data['user_id_marketing_dept'] = 0;
            }

            if ($data['user_id_sales_dept'] ?? '') { /*業務部門(會員編號)*/
                if (!isset($db_number_to_user[$data['user_id_sales_dept']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此業務部門']);
                    continue;
                } else {
                    $data['user_id_sales_dept'] = $db_number_to_user[$data['user_id_sales_dept']]->id;
                }
            } else {
                $data['user_id_sales_dept'] = 0;
            }

            if ($data['user_id_executive_director'] ?? '') { /*大總監(會員編號)*/
                if (!isset($db_number_to_user[$data['user_id_executive_director']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此大總監']);
                    continue;
                } else {
                    $data['user_id_executive_director'] = $db_number_to_user[$data['user_id_executive_director']]->id;
                }
            } else {
                $data['user_id_executive_director'] = 0;
            }

            if ($data['user_id_center_director'] ?? '') { /*中心總監(會員編號)*/
                if (!isset($db_number_to_user[$data['user_id_center_director']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此中心總監']);
                    continue;
                } else {
                    $data['user_id_center_director'] = $db_number_to_user[$data['user_id_center_director']]->id;
                }
            } else {
                $data['user_id_center_director'] = 0;
            }

            if ($data['user_id_center_founder'] ?? '') { /*中心發起人(會員編號)*/
                if (!isset($db_number_to_user[$data['user_id_center_founder']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此中心發起人']);
                    continue;
                } else {
                    $data['user_id_center_founder'] = $db_number_to_user[$data['user_id_center_founder']]->id;
                }
            } else {
                $data['user_id_center_founder'] = 0;
            }

            if ($data['user_id_lecturer'] ?? '') { /*講師編號*/
                if (!isset($db_number_to_user[$data['user_id_lecturer']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此講師']);
                    continue;
                } else {
                    $data['user_id_lecturer'] = $db_number_to_user[$data['user_id_lecturer']]->id;
                }
            } else {
                $data['user_id_lecturer'] = 0;
            }
            if ($data['user_id_center'] ?? '') { /*中心編號*/
                if (!isset($db_number_to_user[$data['user_id_center']])) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此中心']);
                    continue;
                } else if ($db_number_to_user[$data['user_id_center']]->center_level_id == 0) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '區域中心會員非有效中心']);
                    continue;
                } else {
                    $data['user_id_center'] = $db_number_to_user[$data['user_id_center']]->id;
                }
            } else {
                $data['user_id_center'] = 0;
            }
            if (($data['currency'] ?? '') == '') { /*幣別(判斷分站)*/
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定幣別']);
                continue;
            } else {
                $sub = $currency_to_site[$data['currency']] ?? '';
                if (!$sub) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此幣別']);
                    continue;
                } else {
                    unset($data['currency']);
                    $data['order_number'] = $sub . date('Ymd', $data['create_time']) . CommonService::geraHash(8); /*訂單編號*/
                }
            }
            if (($data['total'] ?? '') == '') {
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定訂單金額']);
                continue;
            }
            /*其他衍生資料*/
            $data['over_time'] = self::get_eff_dateline($data['create_time'] ?? time()); /*有效日期*/
            $data['receipts_state'] = 1;
            $data['transport_state'] = 1;
            $data['status'] = 'Complete';
            $data['discount'] = '[]';

            /*商品資料*/
            $product = $data['product'] ?? [];
            if (count($product) == 0) {
                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無購買商品']);
                continue;
            } else {
                $total = 0;
                $prodcut_error = 0;
                foreach ($product as $p_key => $p_value) {
                    if (($p_value['name'] ?? '') == '') {
                        array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定商品名稱']);
                        $prodcut_error += 1;
                        break;
                    }
                    if (($p_value['price'] ?? '') == '') {
                        array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定商品金額']);
                        $prodcut_error += 1;
                        break;
                    }
                    if (($p_value['num'] ?? '') == '') {
                        array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定商品數量']);
                        $prodcut_error += 1;
                        break;
                    }
                    if ($p_value['distributor_id'] ?? '') { /*供應商編號*/
                        if (!isset($db_number_to_user[$p_value['distributor_id']])) {
                            array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此供應商']);
                            $prodcut_error += 1;
                            break;
                        } else {
                            if ($db_number_to_user[$p_value['distributor_id']]->user_type != 1) {
                                array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '此會員並非供應商']);
                                $prodcut_error += 1;
                                break;
                            } else {
                                $product[$p_key]['distributor_id'] = $db_number_to_user[$p_value['distributor_id']]->id;
                                $product[$p_key]['supplier_bonus'] = $db_number_to_user[$p_value['distributor_id']]->supplier_bonus;
                            }
                        }
                    } else {
                        $product[$p_key]['distributor_id'] = 0;
                        $product[$p_key]['supplier_bonus'] = 1;
                        if ($product[$p_key]['price_supplier'] ?? 0) {
                            array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '有設定供應商結算金額卻未設定供應商']);
                            $prodcut_error += 1;
                            break;
                        }
                    }
                    if (($p_value['product_cate'] ?? '') == '') { /*商品類型*/
                        array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '未設定商品類型']);
                        $prodcut_error += 1;
                        break;
                    } else {
                        if (!isset($arr_product_cate[$p_value['product_cate']])) {
                            array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此商品類型']);
                            $prodcut_error += 1;
                            break;
                        } else {
                            $product[$p_key]['product_cate'] = $arr_product_cate[$p_value['product_cate']]['id'];
                            if ($product[$p_key]['product_cate'] == 1) { /*商品是「投資」*/
                                $product[$p_key]['use_ad'] = 0;
                                $product[$p_key]['bonus_model_id'] = 0;
                                if ($product[$p_key]['price_supplier'] ?? 0) {
                                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '投資商品不可設定供應商結算金額']);
                                    $prodcut_error += 1;
                                    break;
                                }
                            } else {
                                if (($p_value['use_ad'] ?? '') == '') { /*是否套用廣告*/
                                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '消費商品未設定廣告設定']);
                                    $prodcut_error += 1;
                                    break;
                                } else if (!isset($arr_use_ad[$p_value['use_ad']])) {
                                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此廣告設定']);
                                    $prodcut_error += 1;
                                    break;
                                } else {
                                    $product[$p_key]['use_ad'] = $arr_use_ad[$p_value['use_ad']]['id'];
                                }
                                if (($p_value['bonus_model_id'] ?? '') == '') { /*回饋模組*/
                                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '消費商品未設定回饋模組']);
                                    $prodcut_error += 1;
                                    break;
                                } else if (!isset($arr_bonus_models[$p_value['bonus_model_id']])) {
                                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此回饋模組']);
                                    $prodcut_error += 1;
                                    break;
                                } else {
                                    $product[$p_key]['bonus_model_id'] = $arr_bonus_models[$p_value['bonus_model_id']]['id'];
                                }
                            }
                        }
                    }
                    if (($p_value['vip_type_reward'] ?? '') == '') { /*提升會員級別*/
                        $product[$p_key]['vip_type_reward'] = 0;
                    } else {
                        if (!isset($arr_vip_types[$p_value['vip_type_reward']])) {
                            array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '無此提升會員級別']);
                            $prodcut_error += 1;
                            break;
                        } else {
                            $product[$p_key]['vip_type_reward'] = $arr_vip_types[$p_value['vip_type_reward']]['id'];
                        }
                    }
                    /*其他衍生資料*/
                    $product[$p_key]['total'] = $p_value['price'] * $p_value['num'];
                    $total += $product[$p_key]['total'];
                    $product[$p_key]['url'] = '';
                    $product[$p_key]['url2'] = '';
                    $product[$p_key]['info_id'] = '0';
                    $product[$p_key]['type_id'] = '0';
                    $product[$p_key]['key_type'] = 'normal';
                    $product[$p_key]['deal_position'] = '1';
                }
                if ($prodcut_error == 0 && $total != $data['total']) {
                    array_push($array_error_data, ['data' => $data_ori, 'error_msg' => '商品與訂單金額不符']);
                    continue;
                }
            }
            $data['product'] = json_encode($product, JSON_UNESCAPED_UNICODE);

            // 紀錄整理後的訂單資料
            array_push($save_data, $data);
        }

        /*有錯誤資料*/
        if ($array_error_data) {
            return $array_error_data;
        }
        // dump($save_data);exit;

        $save_data_product = [];
        foreach ($save_data as $order) {
            $id = DB::connection('main_db')->table('orderform')->insertGetId($order);
            $product = json_decode($order['product'], true) ?? [];
            foreach ($product as $product_data) {
                $product_data['orderform_id'] = $id;
                array_push($save_data_product, $product_data);
            }
        }
        // dump($save_data_product);
        if (count($save_data_product) > 0) {
            DB::connection('main_db')->table('orderform_product')->insert($save_data_product);
        }
        return [];
    }



    /*取得購物車內的商品*/
    public static function get_Proposal_GetCartData($cart_session, $user_id = 0)
    {
        $Proposal = Proposal::withTeamMembersAndRequire(
            ['GetCartData'],
            [
                'cart_session' => $cart_session,
                'user_id' => $user_id,
            ]
        );
        $Proposal = MemberFactory::createNextMember($Proposal);
        return $Proposal;
    }
    /*取得商品資料*/
    public static function get_orders(Request $request, $admin_type, $my_distributor_id, $state, $need_page = true)
    {
        $order_ship_status = $request->get('order_ship_status') ?? '';

        $order_sql = 'order.create_time desc, id desc';
        if ($state == 'Trash') { // 垃圾桶訂單
            $where = "( (order.status='Cancel') OR (order.status='Return') )";
            $order_sql = 'order.cancel_date desc';
        } else if ($state == 'New') { // 新進訂單
            if ($order_ship_status == '-1') { // 全部(非垃圾桶)
                $where = "( order.status NOT IN ('Cancel', 'Return') )";
            } else if ($order_ship_status == '1') { // 待揀貨
                $where = "( order.status='New' AND (order.receipts_state='0' AND order.payment!='1') )";
            } else if ($order_ship_status == '2') { // 可揀貨
                $where = "( order.status='New' AND NOT (order.receipts_state='0' AND order.payment!='1') )";
            } else if ($order_ship_status == '3') { // 待包裝
                $where = "( order.status='Pickable' )";
            } else if ($order_ship_status == '4') { // 可寄出
                $where = "( order.status='Picked' )";
            } else if ($order_ship_status == '5') { // 已出貨
                $where = "( order.status='Complete' )";
            } else { // 其他
                $where = "( order.status='New' )";
            }
        } else if ($state == 'Complete') { // 完成訂單
            $where = "( order.status='Complete' )";
        } else {
            $where = "( order.status='" . $state . "' )";
        }

        $receipts_state = $request->get('receipts_state') ?? '';
        if ($receipts_state !== '') {
            $where .= " AND order.receipts_state='" . $receipts_state . "'";
        }

        $payment = $request->get('payment') ?? '';
        if ($payment !== '') {
            $where .= " AND order.payment='" . $payment . "'";
        }

        $transport = $request->get('transport') ?? '';
        if ($transport !== '') {
            $where .= " AND order.transport='" . $transport . "'";
        }

        $transport_location_name = $request->get('transport_location_name') ?? '';
        if ($transport_location_name !== '') {
            $where .= " AND order.transport_location_name='" . $transport_location_name . "'";
        }

        $transport_location_phone = $request->get('transport_location_phone') ?? '';
        if ($transport_location_phone !== '') {
            $where .= " AND order.transport_location_phone='" . $transport_location_phone . "'";
        }

        $stock_status = $request->get('stock_status') ?? '';
        if ($stock_status !== '') {
            $where .= " AND order.stock_status='" . $stock_status . "'";
        }
        $do_award_time = $request->get('do_award_time') ?? '';
        if ($do_award_time !== '') {
            if ($do_award_time == '0') { /*尚未回饋*/
                $where .= " AND order.do_award_time=''";
            } else if ($do_award_time == '-1') { /*已回饋*/
                $where .= " AND order.do_award_time!=''";
            } else {
                $where .= " AND UNIX_TIMESTAMP(order.do_award_time)='" . $do_award_time . "'";
            }
        }

        $searchKey2 = $request->get('searchKey2') ?? '';
        $searchKey2 = trim($searchKey2);
        $searchKey2_exclude = $request->get('searchKey2_exclude') ?? '';
        if ($searchKey2 != '') {
            $like_condition = $searchKey2_exclude == '排除' ? ' NOT LIKE ' : ' LIKE ';
            $where .= " AND order.product " . $like_condition . " '%" . $searchKey2 . "%'";
        }
        $buy_date_st2 = $request->get('buy_date_st2') ?? '';
        $buy_date_en2 = $request->get('buy_date_en2') ?? '';
        if ($buy_date_st2 != '') {
            $where .= " AND order.create_time > '" . strtotime($buy_date_st2) . "'";
        }
        if ($buy_date_en2 != '') {
            $where .= " AND order.create_time <'" . (strtotime($buy_date_en2) + 86400) . "'";
        }

        $pre_buy = $request->get('pre_buy') ?? '';
        if ($pre_buy != '') {
            $where .= " AND order.product REGEXP '\"pre_buy\":\"1\"";
        }
        $where = str_replace('\\', '\\\\', $where);
        // dump($where);exit;
        $rowData = DB::connection('main_db')->table('orderform as order')->select('order.*', 'account.name', 'account.number')->leftJoin('account', 'account.id', '=', 'order.user_id')->whereRaw($where);

        $distributor_id = 0;
        $distributor_id_where = '';
        if ($admin_type == 'distribution') { /*供應商限看自己的*/
            $distributor_id_where = 'distributor_id="' . $my_distributor_id . '"';
        } else if ($admin_type == 'admin') { /*若為管理者，可看指定供應商*/
            $distributor_id = $request->get('distributor_id') ?? $my_distributor_id;
            if ($distributor_id !== '' && $distributor_id !== '-1') {
                $distributor_id_where = 'distributor_id="' . $distributor_id . '"';
            }
        }
        // dump($distributor_id_where);exit;
        if ($distributor_id_where) {
            $rowData = $rowData->whereRaw($distributor_id_where);
        }

        $searchKey = $request->get('searchKey') ?? '';
        $searchKey = trim($searchKey);
        $rowData = $rowData->whereRaw("(
                              order.order_number LIKE '%$searchKey%' OR
                              order.transport_location_name LIKE '%$searchKey%' OR
                              order.transport_location_phone LIKE '%$searchKey%'
                            )")
            ->orderByRaw($order_sql);
        // dump($where);exit;
        $page_count = $request->get('page_count') ?? self::PER_PAGE_ROWS;
        if ($need_page) {
            $rowData = $rowData->paginate($page_count)
                ->appends([
                    'order_ship_status' => $order_ship_status,
                    'state' => $state,
                    'receipts_state' => $receipts_state,
                    'payment' => $payment,
                    'transport' => $transport,
                    'transport_location_name' => $transport_location_name,
                    'transport_location_phone' => $transport_location_phone,
                    'stock_status' => $stock_status,
                    'do_award_time' => $do_award_time,
                    'searchKey2' => $searchKey2,
                    'searchKey2_exclude' => $searchKey2_exclude,
                    'buy_date_st2' => $buy_date_st2,
                    'buy_date_en2' => $buy_date_en2,
                    'pre_buy' => $pre_buy,
                    'distributor_id' => $distributor_id,
                    'searchKey' => $searchKey,
                    'page_count' => $page_count,
                ]);
        } else {
            $rowData = $rowData->get();
            $rowData = CommonService::objectToArray($rowData);
        }
        return $rowData;
    }

    /**
     * 取得指定訂單的商品
     */
    public static function get_orderform_products(array $orderform_ids, string $key_type = '', array $param = [])
    {
        $orderform_products = Db::connection('main_db')->table('orderform_product')->whereIn('orderform_id', $orderform_ids);
        if ($key_type) {
            if ($key_type == '-1') { /*排除運費*/
                $orderform_products = $orderform_products->whereNotNull('key_type');
            } else if ($key_type == '-2') { /*撈取productinfo商品*/
                // 沒優惠的商品
                $orderform_products = $orderform_products->whereNotNull('key_type')->whereNotIn('key_type', Proposal::NOT_PRODUCTINFO_PRODTYPE);
            } else {
                $orderform_products = $orderform_products->where('key_type', $key_type);
            }
        }

        //有id的話就只撈取指定id的資料
        if (isset($param['orderform_product_id'])) {
            $orderform_product_id = $param['orderform_product_id'];
            if ($orderform_product_id) {
                $orderform_products->where('id', $orderform_product_id);
            }
        }

        //商品名稱篩選
        $searchKey2 =  trim($param['searchKey2'] ?? '');
        $searchKey2_exclude = $param['searchKey2_exclude'] ?? '';

        if ($searchKey2 != '') {
            $like_condition = $searchKey2_exclude == '排除' ? 'NOT LIKE' : 'LIKE';
            $orderform_products->where('name', $like_condition, '%' . $searchKey2 . '%');
        }

        //供應商回饋方式篩選
        if (isset($param['supplier_bonus'])) {
            $supplier_bonus = $param['supplier_bonus'];
            if ($supplier_bonus !== '') {
                $orderform_products->where('supplier_bonus', '=', $supplier_bonus);
            }
        }

        /*篩選有設定供應商結算金額*/
        if ($param['has_price_supplier'] ?? '') {
            $orderform_products->where('price_supplier', '>', 0);
        }

        //供應商回饋時間篩選
        if (isset($param['do_award_supplier_time'])) {
            $do_award_supplier_time = $param['do_award_supplier_time'];
            if ($do_award_supplier_time !== '') {
                if ($do_award_supplier_time == '0') { /*尚未回饋*/
                    $orderform_products->where('do_award_supplier_time', '=', '');
                } else if ($do_award_supplier_time == '-1') { /*已回饋*/
                    $orderform_products->where('do_award_supplier_time', '!=', '');
                }
            }
        }

        //供應商回饋時間區間篩選
        if (isset($param['do_award_supplier_time_s'])) {
            $do_award_supplier_time_s = $param['do_award_supplier_time_s'];
            if ($do_award_supplier_time_s !== '') {
                $do_award_supplier_time_s = strtotime($do_award_supplier_time_s);
                $orderform_products->where('do_award_supplier_time', '>=', $do_award_supplier_time_s);
            }
        }
        if (isset($param['do_award_supplier_time_e'])) {
            $do_award_supplier_time_e = $param['do_award_supplier_time_e'];
            if ($do_award_supplier_time_e !== '') {
                $do_award_supplier_time_e = strtotime($do_award_supplier_time_e . ' +1Day');
                $orderform_products->where('do_award_supplier_time', '<', $do_award_supplier_time_e)
                    ->where('do_award_supplier_time', '!=', '');
            }
        }

        // 供應商篩選
        if (isset($param['distributor_id'])) {
            $distributor_id = $param['distributor_id'];
            if ($distributor_id == '-1') { /*不篩選供應商*/
            } else if ($distributor_id == '-2') { /*篩選有供應商的*/
                $orderform_products->where('distributor_id', '!=', 0);
            } else {  /*篩選指定供應商*/
                $orderform_products->where('distributor_id', '=', $distributor_id);
            }
        }

        // 供應商篩選(複選)
        if (isset($param['distributor_ids'])) {
            $distributor_ids = $param['distributor_ids'];
            if (count($distributor_ids) > 0) {
                $orderform_products->whereIn('distributor_id', $distributor_ids);
            }
        }

        // 排序
        $order_query = $param['order_query'] ?? '';
        if ($order_query) {
            $orderform_products = $orderform_products->orderByRaw($order_query);
        }

        // 分頁
        $need_page = $param['need_page'] ?? false;
        if ($need_page) {
            $page_count = $param['page_count'] ?? self::PER_PAGE_ROWS;
            $orderform_products = $orderform_products->paginate($page_count)
                ->appends([]);
        } else {
            $orderform_products = $orderform_products->get();
            $orderform_products = CommonService::objectToArray($orderform_products);
        }

        return $orderform_products;
    }

    /*取得超額購買數組*/
    public static function get_pre_buy_num_group($cart_key, $buy_num, $userId)
    {
        $pre_buy_num_group = [];
        if (config('control.control_pre_buy')) { /*有啟用超額購買功能*/
            $type_id = explode('_', $cart_key)[0];
            $stock = DB::table('productinfo_type')->find($type_id);
            $stock = CommonService::objectToArray($stock);

            $same_type_product_num = [];    /*同品項的各類型商品在購物車中的數量(區分一般、網紅、加價購...)*/
            $Proposal = self::get_Proposal_GetCartData('cart', $userId);
            $cart = $Proposal->getCartArray();
            foreach ($cart as $key => $value) {
                if ($key == $cart_key) {  /*如果是操作中的品項*/
                    $num = $buy_num;    /*依修正數量*/
                } else if (preg_match('/' . $type_id . '_/', $key)) {
                    $num = $value;      /*依購物車紀錄*/
                } else {
                    continue;           /*略過不同品項的商品*/
                }
                array_push($same_type_product_num, $num);
            }
            if (array_sum($same_type_product_num) > $stock['num']) {
                $elements = CommonService::elements_able_to_sum_to_target($same_type_product_num, $stock['num']); /*是否能剛好組成等於庫存數量*/
                foreach ($same_type_product_num as $key => $value) {
                    $in_array_index = array_search($value, $elements);
                    if ($in_array_index !== false) { /*是組成超額購買的數量*/
                        array_splice($elements, $in_array_index, 1);
                    } else {
                        array_push($pre_buy_num_group, $value);
                    }
                }
            }
        }
        return $pre_buy_num_group;
    }

    /*取得運送方法*/
    public static function get_shipping_method($cartData, $shipping_id = 0)
    {
        /*取得運送方法*/
        //--- 刪去法: 可運運法為所有運法的交集
        if (config('control.control_product_shipping') == 1) { /* 有啟用商品關聯運法 */
            foreach ($cartData as $key => $value) {
                if ($value['shipping_type'] == "") { // 無勾選運法
                    if (!isset($shipping_type)) { // 還未設定過可運運法
                        $shipping_type = [];
                        $shipping_all = DB::table('shipping_fee')->orderByRaw('order_id asc, id desc')->get();
                        $shipping_all = CommonService::objectToArray($shipping_all);
                        array_walk($shipping_all, function ($item) use (&$shipping_type) {
                            array_push($shipping_type, $item['id']);
                        });
                    }
                } else { // 有勾選運法
                    if (!isset($shipping_type)) { // 還未設定過可運運法
                        $shipping_type = explode(',', $value['shipping_type']);
                    } else {
                        $shipping_type = array_intersect($shipping_type, explode(',', $value['shipping_type']));
                    }
                }
            }
            $shipping_where = isset($shipping_type) ? array_filter($shipping_type, function ($v) {
                return $v != null;
            }) : [];
            $shipping_where = $shipping_where ? "id in (" . join(',', $shipping_where) . ")" : 'id=-1';
        } else {
            $shipping_where = '1=1';
        }

        $shipping_fee = DB::table('shipping_fee')->whereRaw($shipping_where);
        if ($shipping_id) {
            $shipping_fee = $shipping_fee->where('id', $shipping_id);
        }
        $shipping_fee = $shipping_fee->orderByRaw('order_id asc, id desc')->get();
        $shipping_fee = CommonService::objectToArray($shipping_fee);
        foreach ($shipping_fee as $key => $value) {
            // $value['name'] = str_replace('\\', '\\\\', $value['name']);

            /*合併供應商設定資料*/
            if (config('control.control_platform') == 1) {
                $distributor_id = $cartData[0]['distributor_id'];
            } else {
                $distributor_id = 0;
            }
            $admin_type = $distributor_id == 0 ? 'admin' : 'distribution';
            $shipping_fee[$key] = CartMethod::merge_distributor_setting($value, 'shipping_fee', $distributor_id);
            if (!isset($shipping_fee[$key]['shipping_fee_id'])) {
                $shipping_fee[$key]['shipping_fee_id'] = $value['id'];
            }
        }
        $shipping_fee = array_filter($shipping_fee, function ($v) {
            return $v['online'] == 1;
        });

        /*調整運費*/
        if (empty(config('control.close_function_current')['運費標籤管理'])) { /*如果有使用運費標籤管理功能*/
            $calculated_shipping_fee = 0;
            foreach ($cartData as $key => $value) {
                if ($value['key_type'] == 'add') {
                    continue;
                } /*加價購商品不計算運費*/
                $shipping_fee_tag = DB::table('shipping_fee_tag')->whereRaw('id="' . $value['shipping_fee_tag'] . '"')->first();
                $shipping_fee_tag = CommonService::objectToArray($shipping_fee_tag);
                if ($shipping_fee_tag) { /*有找到套用的運費標籤*/
                    $calculated_shipping_fee += ($shipping_fee_tag['price'] * $value['num']); /*累計運費*/
                }
            }

            /*修改所有運法的金額為計算出來的運費*/
            foreach ($shipping_fee as $key => $value) {
                $shipping_fee[$key]['price'] = $calculated_shipping_fee;
            }
        }

        // dd($shipping_fee);
        return $shipping_fee;
    }

    /*生成訂單編號*/
    public static function create_order_number()
    {
        $count = DB::connection('main_db')->table('orderform')->whereRaw('order_number like "' . config('extra.shop.subDeparment') . 'O' . date('Ymd') . '%"')->orderBy('id', 'desc')->first();
        $count = $count ? intval(substr($count->order_number, -3)) + 1 : 1;
        if ($count < 10) {
            $count = '00' . $count;
        } else if ($count < 100) {
            $count = '0' . $count;
        }
        return config('extra.shop.subDeparment') . 'O' . date('Ymd') . CommonService::randomkeys(5) . $count;
    }

    /*取得紅利點數到期時間*/
    public static function get_eff_dateline($time = null)
    {
        $time = $time ? $time : time();
        $Eff = config('control.control_point_duration');            //Sql給予期限
        $Eff_data = config('control.control_point_duration_date');  //Sql給予期限  12-31
        $Y = (date("Y", $time) + $Eff) . '-' . $Eff_data . ' 23:59:59';      //取得年份
        return $Y;
    }

    /*清除購物車內紀錄(減少庫存)*/
    public static function clearCart($user_id)
    {
        $Proposal = self::get_Proposal_GetCartData('cart_all', $user_id);
        $cart_all = $Proposal->getCartArray();

        $Proposal = self::get_Proposal_GetCartData('cart', $user_id);
        $cart = $Proposal->getCartArray();
        foreach ($cart as $cart_key => $num) {
            [$type_id_ori, $key_type] = Proposal::get_prod_key_type($cart_key);
            switch ($key_type) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($key_type, 0, 3) == 'kol':
                case 'add':
                case substr($key_type, 0, 8) == 'askprice':
                    /*設定詢價為已購買*/
                    if (substr($key_type, 0, 8) == 'askprice') {
                        $askprice_id = str_replace('askprice', '', $key_type);
                        $AskpriceHelper = new AskpriceHelper();
                        $result = $AskpriceHelper->getOne_by_main_id($askprice_id, 'a.user_id ="' . $user_id . '"');
                        $main = $result['main'];
                        if (!$main) {
                            break;
                        }
                        $main_record = $result['current'];
                        if (!$main_record) {
                            break;
                        }
                        DB::table('askprice_record')->where('id', $main_record['id'])->update(['bought' => 1]);
                    }
                    /*只扣線上可購買數量*/
                    DB::table('productinfo_type')->where('id', $type_id_ori)->decrement('num', $num);
                    break;

                case 'coupon': // 優惠券商品
                    break;
            }
            /*刪除加入購物車紀錄*/
            unset($cart_all[$cart_key]);
        }
        session()->put('cart_all', json_encode($cart_all));
        session()->forget('cart');
    }

    /*依項檢查商品是否可以操作購物車*/
    public static function check_status_can_add_cart($num_input, $type_id = 0, $cmd = "", $prod_type = 'coupon', $distributor_id = '')
    { /*數量, 品項id、 操作方式、 商品類型、 欲加入購物車的賣家id*/
        $target_p = null;
        if (!in_array($prod_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
            $target_p = DB::table('productinfo_type as pt')->join('productinfo as p', 'pt.product_id', '=', 'p.id')
                // ->where('p.online in (0,1)')
                ->where('pt.closed', 0)
                ->select(
                    'pt.num',
                    'pt.title',
                    'pt.online',
                    'pt.start_time',
                    'pt.end_time',
                    'pt.closed',
                    'p.title as pro_name',
                    'p.card_pay',
                    'p.online as p_online',
                    'p.distributor_id',
                    'p.final_array'
                )
                ->where('pt.id', $type_id)
                ->first();
            $target_p = CommonService::objectToArray($target_p);
            if (!$target_p) {
                return [
                    'code' => 0,
                    'msg' => Lang::get('資料有誤'),
                    'num' => $num_input,
                ];
            }

            if ($cmd != 'delete') { /*刪除以外的操作才需檢查*/
                /*需檢查賣家 且 賣家不相同*/
                if ($distributor_id != '' && $distributor_id != $target_p['distributor_id']) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('選擇的商品包含不同賣家，無法同時結帳') . '：' . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }

                // 檢查商品是否上架
                $respData = ProductHelpler::check_product_and_infotype_close($target_p['final_array']);
                if ($target_p['p_online'] == 2 || $respData->close) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已下架') . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否被假刪除
                if ($target_p['online'] == 0) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已被刪除') . $target_p['pro_name'] . '的品項:' . $target_p['title'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否被取消
                if ($target_p['closed'] == 1) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已被取消的品項') . $target_p['pro_name'] . '的品項:' . $target_p['title'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否在顯示期限內
                if (
                    ($target_p['start_time'] != "" && $target_p['start_time'] > date("Y-m-d")) or
                    ($target_p['end_time'] != "" && $target_p['end_time'] < date("Y-m-d"))
                ) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('不在顯示期限內') . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }
            }
        }
        return [
            'code' => 1,
            'target_p' => $target_p,
            'msg' => 'OK',
            'num' => $num_input,
        ];
    }
    public static function set_cart_session($cart_session, $cmd, $cart_key, $num, $user_id = 0)
    {
        $Proposal = Proposal::withTeamMembersAndRequire(
            [
                'GetCartData',
                'Assign',
                'Increase',
                'Delete',
                'Decrease'
            ],
            [
                'cart_session' => $cart_session,
                'cmd' => $cmd,
                'user_id' => $user_id,
                'id' => $cart_key,
                'num' => $num
            ]
        );
        $Proposal = MemberFactory::createNextMember($Proposal);
        session()->put($cart_session, $Proposal->projectData['data']);
    }

    /*檢查庫存*/
    public static function check_stock()
    {
        $orderforms_check_stock = [];
        $productinfo_type_num = [];
        $order_product = DB::connection('main_db')->table('orderform_product AS op')
            ->selectRaw('op.*, o.order_number')
            ->join('orderform AS o', 'o.id', 'op.orderform_id')
            ->whereRaw('o.status="New"')
            ->whereRaw('o.stock_status=0')
            ->whereRaw('op.info_id IS NOT NULL AND op.type_id IS NOT NULL AND op.key_type IS NOT NULL')
            ->orderBy('op.id', 'asc')
            ->get();
        $order_product = CommonService::objectToArray($order_product);
        foreach ($order_product as $product) {
            if (!isset($orderforms_check_stock[$product['orderform_id']])) {
                $orderforms_check_stock[$product['orderform_id']] = true;
            }
            if (in_array($product['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*非「商品」，免檢查庫存*/
                continue;
            } else if ($product['pre_buy'] == 0) { /*非超額購買，能成立訂單必定有庫存*/
                continue;
            } else { /*是「商品」且有超額購買，要檢查「實體庫存」是否夠*/
                if (!isset($productinfo_type_num[$product['type_id']])) {
                    $arr_db = OrderHelper::get_shop_db_config($product['order_number']);
                    $r = DB::connection($arr_db)->table('productinfo_type')
                        ->select('num')
                        ->whereRaw("id='" . $product['type_id'] . "' AND product_id='" . $product['info_id'] . "'")
                        ->first();
                    $r = CommonService::objectToArray($r);
                    $productinfo_type_num[$product['type_id']] = $r['num'] ?? -999999;
                }
                /*只要線上可購買數量小於0就視為庫存不足*/
                if ($productinfo_type_num[$product['type_id']] < 0) {
                    $orderforms_check_stock[$product['orderform_id']] = false;
                }
            }
        }
        // dump($productinfo_type_num);
        // dump($orderforms_check_stock);
        // exit;
        foreach ($orderforms_check_stock as $orderform_id => $has_stock) {
            if ($has_stock) {
                DB::connection('main_db')->table('orderform')->where('id', $orderform_id)->update(['stock_status' => 1]);
            }
            // else{
            //     DB::connection('main_db')->table('orderform')->where('id', $orderform_id)->update(['stock_status' => 0]);
            // }
        }
    }


    /*天脈:處理積分回饋*/
    public static function do_pointback(array $orderform_ids)
    {
        /*檢查要處理的訂單們*/
        $orderforms = [];

        foreach ($orderform_ids as $orderform_id) {
            $orderform = Db::connection('main_db')->table('orderform')->where('id', $orderform_id)->first();
            $orderform = CommonService::objectToArray($orderform);
            // dump($orderform);
            if (!$orderform) {
                throw new \Exception(Lang::get('連結有誤'));
            }
            if ($orderform['status'] != 'Complete') {
                throw new \Exception(Lang::get('訂單未完成，無法處理回饋'));
            }
            if ($orderform['receipts_state'] == '0') {
                throw new \Exception(Lang::get('訂單未付款，無法處理回饋'));
            }
            if ($orderform['do_award_time']) {
                throw new \Exception(Lang::get('訂單已完成過處理回饋'));
            }
            if (!$orderform['user_id_marketing_dept']) {
                throw new \Exception(Lang::get('訂單未設定行政廣告部門'));
            }
            if (!$orderform['user_id_lecturer']) {
                throw new \Exception(Lang::get('訂單未設定講師'));
            }
            if (!$orderform['user_id_center']) {
                throw new \Exception(Lang::get('訂單未設定中心會員'));
            }
            array_push($orderforms, $orderform);
        }

        $member_system_id = config('extra.skychakra.member_system'); /*系統帳戶id*/
        $member_month_divided_id = config('extra.skychakra.member_month_divided'); /*月分紅帳戶id*/


        /*逐個訂單處理回饋(每筆訂單透過分潤實體統計完後，必須執行一次send_by_cal，才可換下筆訂單)*/
        foreach ($orderforms as $orderform) {
            $now_time = time();

            try {
                /*標記訂單已完成處理回饋(先標記，因為回饋處理時間較長，怕有人同時操作)*/
                Db::connection('main_db')->table('orderform')->where('id', $orderform['id'])->update(['do_award_time' => $now_time]);
                Db::connection('main_db')->table('orderform_product')->where('orderform_id', $orderform['id'])->where('supplier_bonus', 1)->update(['do_award_supplier_time' => $now_time]);

                $BonusHelper = new BonusHelper($now_time);
                /*設定訂單相關資訊*/
                $BonusHelper->set_orderform_id($orderform['id']);
                $BonusHelper->set_order_number($orderform['order_number']);

                /*組織相關回饋人員(訂單) start --------------------*/
                /*購買者*/
                $user_id = $orderform['user_id'];
                $BonusHelper->init_user_set($user_id);

                /*購買者的推薦者 (上線)*/
                $upline_user = $BonusHelper->get('user_cal')[$user_id]['data']['upline_user'] ?? 0;
                $BonusHelper->init_user_set($upline_user);

                /*購買者的推薦者的推薦者 (上線的上線)*/
                $top_line_user = $BonusHelper->get('user_cal')[$upline_user]['data']['upline_user'] ?? 0;
                $BonusHelper->init_user_set($top_line_user);

                /*營運者 - 5個角色*/
                $user_id_marketing_dept = $orderform['user_id_marketing_dept'] ?? 0; // 行政廣告部門
                $user_id_sales_dept = $orderform['user_id_sales_dept'] ?? 0; // 業務部門
                $user_id_executive_director = $orderform['user_id_executive_director'] ?? 0; // 大總監
                $user_id_center_director = $orderform['user_id_center_director'] ?? 0; // 中心總監
                $user_id_center_founder = $orderform['user_id_center_founder'] ?? 0; // 中心發起人

                $BonusHelper->init_user_set($user_id_marketing_dept);
                $BonusHelper->init_user_set($user_id_sales_dept);
                $BonusHelper->init_user_set($user_id_executive_director);
                $BonusHelper->init_user_set($user_id_center_director);
                $BonusHelper->init_user_set($user_id_center_founder);

                /*講師*/
                $user_id_lecturer = $orderform['user_id_lecturer'] ?? 0;
                $BonusHelper->init_user_set($user_id_lecturer);

                /*中心*/
                $user_id_center = $orderform['user_id_center'] ?? 0;
                $BonusHelper->init_user_set($user_id_center);

                /*上層中心*/
                $user_id_center_upper = $BonusHelper->get('user_cal')[$user_id_center]['data']['upline_user'] ?? 0;
                $BonusHelper->init_user_set($user_id_center_upper);
                /*組織相關回饋人員(訂單) end -------------------------*/

                /*設定處理回饋所需資料 start -------------------------*/
                $BonusHelper->set_buyer_id($user_id); //購買者
                $BonusHelper->set_buyer_topline_id($upline_user); //購買者的推薦者

                // 設定新CV分配機制所需的部門會員ID上下文
                $BonusHelper->set_context_marketing_dept_id($user_id_marketing_dept);
                $BonusHelper->set_context_sales_dept_id($user_id_sales_dept);
                $BonusHelper->set_context_center_id($user_id_center);

                /*設定處理回饋所需資料 end -----------------------------*/

                /*(購買者來源判斷 2廣告，1直接)(預設是「直接」訂單)*/
                $registration_from = $BonusHelper->get('user_cal')[$user_id]['data']['registration_from'] ?? 1;

                /*逐個商品統計數值(僅撈取productinfo商品)*/
                $products = self::get_orderform_products([$orderform['id']], '-2'); //沒打折的商品
                // dump($products);
                foreach ($products as $product) {

                    /*取得商品「校正CV金額」*/
                    $count_cv = $BonusHelper->get_count_cv($product);

                    if ($product['product_cate'] == 1) { /*是「投資商品」*/
                        /*購買者可累積「投資金額」*/
                        $BonusHelper->add_total_invest($user_id, $count_cv);
                    } else { /*消費商品*/
                        /*商品供應商可獲得「供應商回饋」*/
                        $BonusHelper->add_supplier_bonus($product);

                        /* 購買者可獲得「消費圓滿點數」(線上消費回饋消費圓滿點數) ----------- */
                        $BonusHelper->add_limit_consumption($user_id, $count_cv);

                        $newVipLevel = 0; //新會員等級
                        if ($product['vip_type_reward']) { /*消費商品-課程*/
                            /*購買者可調整「會員級別」*/
                            $newVipLevel = $BonusHelper->set_final_vip_type($user_id, $product['vip_type_reward'], 1);
                            /*購買者可調整「課程進度」*/
                            $BonusHelper->set_final_vip_type($user_id, $product['vip_type_reward'], 2);
                        }
                        /* ---------------------------------------------------------- */

                        /*各項應分潤CV金額計算*/
                        if ($product['use_ad'] == 0) { /* 商品沒套用「廣告」，進行「一般分潤回饋」*/
                            $BonusHelper->add_pi_pool($count_cv); /*累計到資金池異動*/

                            /*取得商品「分享CV金額」*/
                            $share_cv = $BonusHelper->count_share_cv($count_cv);

                            $share_cv_allocated = 0; /*計算已分配的CV金額*/

                            /* 計算 購買者的推薦者 的「應分潤金額」(推廣獎勵) */
                            /* 依商品與會員，調整分配給推薦者經燒傷後的「分享CV金額」*/
                            /// 師爺來翻譯一下燒傷:
                            /// A會員是中脈級別，所以分潤的CV範圍是3740 (vip_type.burn_cv)
                            /// A介紹了B會員，B會員升到了更高級的法身級別。
                            /// 因此跟B有關的分潤部分，A都必須限制在自己等級內的3740
                            /// 這就是燒傷
                            if ($newVipLevel > 0 && $registration_from == 2) {
                                // 20250805增加新規則，廣告會員購買課程，由全體合伙人依個人上課程度燒傷及合伙人級別權值領取
                                /*獲取所有有效合伙人*/
                                $weight_result = $BonusHelper->get_active_partner_weight_result($user_id);
                                $total_weight = $weight_result['total_weight'];
                                $active_partners = $weight_result['user_weight'];
                                Log::info("有效合伙人", ['合伙人' => $active_partners, '總權重' => $total_weight]);

                                /*計算推廣獎勵部分的CV金額*/
                                $recommend_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 1, $share_cv);
                                $share_cv_allocated += $recommend_cv;

                                /*依據課程類型和合伙人課程進度進行燒傷計算和分配*/
                                if ($total_weight > 0) {
                                    /*根據購買的課程類型計算分段CV金額*/
                                    $course_segments = self::calculateCourseSegments($product, $newVipLevel, $recommend_cv, $BonusHelper);

                                    /*為每個課程段分別分配給符合條件的合伙人*/
                                    foreach ($course_segments as $segment) {
                                        $segment_cv = $segment['cv'];
                                        $required_level = $segment['required_level'];

                                        /*篩選符合該段要求的合伙人*/
                                        $qualified_partners = [];
                                        $qualified_total_weight = 0;

                                        foreach ($active_partners as $partner_id => $weight) {
                                            $partner_course_level = $BonusHelper->user_cal[$partner_id]['data']['vip_type_course'] ?? 0;
                                            if ($partner_course_level >= $required_level) {
                                                $qualified_partners[$partner_id] = $weight;
                                                $qualified_total_weight += $weight;
                                            }
                                        }

                                        /*分配該段CV給符合條件的合伙人*/
                                        $partners_with_rewards = []; // 收集拿到推廣獎勵的合夥人
                                        if ($qualified_total_weight > 0) {
                                            foreach ($qualified_partners as $partner_id => $weight) {
                                                $weight_ratio = $qualified_total_weight > 0 ? (float)($weight / $qualified_total_weight) : 0;
                                                $partner_cv = $segment_cv * $weight_ratio;

                                                /*應用燒傷限制*/
                                                $partner_course_level = $BonusHelper->user_cal[$partner_id]['data']['vip_type_course'] ?? 0;
                                                $partner_vip_type = $BonusHelper->arr_vip_types[$partner_course_level] ?? [];
                                                $partner_burn_cv = $partner_vip_type['burn_cv'] ?? 0;

                                                if ($partner_burn_cv > 0 && $partner_cv > $partner_burn_cv) {
                                                    $partner_cv = $partner_burn_cv;
                                                }

                                                /*分配給合伙人*/
                                                if ($partner_cv > 0) {
                                                    $BonusHelper->add_available_cv($partner_id, $partner_cv);
                                                    $partners_with_rewards[] = $partner_id; // 記錄拿到獎勵的合夥人
                                                    Log::info("收到推廣獎勵合伙人", [$partner_id]);
                                                }
                                            }

                                            /** 所有拿到推廣獎勵的合夥人的上線合夥人，共同分配合夥平級獎勵 */
                                            if (!empty($partners_with_rewards)) {
                                                $partner_bonus_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 2, $share_cv);
                                                $share_cv_allocated += $partner_bonus_cv;
                                                self::distributePartnerBonusToDirectUplines($BonusHelper, $partners_with_rewards, $partner_bonus_cv);
                                            }
                                        }
                                    }
                                }
                            } else {
                                //直推會員購買課..依上線的vip等級取得可分的cv值.這裡不變
                                if ($upline_user > 0) { // 檢查是否有推薦者
                                    $share_cv_vip_type_burn = $BonusHelper->count_share_cv_vip_type($product, $upline_user);
                                    $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 1, $share_cv_vip_type_burn);
                                    $share_cv_allocated += $available_cv;
                                    $BonusHelper->add_available_cv($upline_user, $available_cv);
                                }

                                /*計算 購買者的推薦者的推薦者 的「應分潤金額」(合夥平級獎勵)*/
                                $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 2, $share_cv);
                                $share_cv_allocated += $available_cv;

                                //20250807修改: 分配到推廣獎的合夥人，如果上線也是合夥人，上線依自己的合夥人階級比例領取合夥獎勵
                                /*購買者的推薦者分配給其直接上線合夥人（函數內部會檢查是否為合夥人）*/
                                self::distributePartnerBonusToDirectUplines($BonusHelper, [$upline_user], $available_cv);
                            }

                            /** ------ 原營運奬勵 拆成5個角色 ----------- */

                            /*計算 行政廣告部門 的「應分潤金額」*/
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 3, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_marketing_dept, $available_cv);

                            /**計算 業務部門 的「應分潤金額」 */
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 4, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_sales_dept, $available_cv);

                            /**計算 大總監 的「應分潤金額」 */
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 5, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_executive_director, $available_cv);

                            /**計算 中心總監 的「應分潤金額」 */
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 6, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_center_director, $available_cv);

                            /**計算 中心發起人 的「應分潤金額」 */
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 7, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_center_founder, $available_cv);

                            /*營運者可額外獲得「其他圓滿點數」(2024-11-17 天脈決定不回饋「其他圓滿點數」)*/
                            // $BonusHelper->add_limit_other($user_id_marketing_dept, $available_cv);
                            /** ------------------------------------- */

                            /*計算 講師 的「應分潤金額」(講師獎勵)*/
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 8, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($user_id_lecturer, $available_cv);
                            /*講師可額外獲得「其他圓滿點數」(2024-11-17 天脈決定不回饋「其他圓滿點數」)*/
                            // $BonusHelper->add_limit_other($user_id_lecturer, $available_cv);

                            /*計算 中心 的「應分潤金額」(中心獎勵)----------------*/
                            /*取得「應分潤金額」*/
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 9, $share_cv);

                            /*計算CV分潤比率*/
                            $user_id_center_level = $BonusHelper->get('user_cal')[$user_id_center]['data']['center_level_id'] ?? 0; //取得訂單用戶的中心等級id
                            $user_id_center_upper_level = $BonusHelper->get('user_cal')[$user_id_center_upper]['data']['center_level_id'] ?? 0; //取得上層中心的等級id
                            $center_level_diff_result = $BonusHelper->count_center_level_diff($user_id_center_level, $user_id_center_upper_level);

                            $level_ratio = $center_level_diff_result['level_weight']; //計算後的上層比率，下層比率

                            /*給本層中心獎勵(按「發起者占比」拆分)(含「其他圓滿點數」的累積)*/
                            $divided_num_lower = (float)($available_cv * $level_ratio['center_lower'] / 100); //本層中心能分到奬金 (cv_ratio需要除以100)
                            $share_cv_allocated += $divided_num_lower;
                            $BonusHelper->add_available_cv_center($user_id_center, $divided_num_lower, $product['bonus_model_id']);

                            /*給上層中心獎勵(按「發起者占比」拆分)(含「其他圓滿點數」的累積)*/
                            $divided_num_upper = (float)($available_cv * $level_ratio['center_upper'] / 100); //上層中心能分到奬金 (cv_ratio需要除以100)
                            $share_cv_allocated += $divided_num_upper;
                            $BonusHelper->add_available_cv_center($user_id_center_upper, $divided_num_upper, $product['bonus_model_id']);
                            /* -------- end (中心獎勵) --------------------------------------- */

                            /*計算 月分紅帳戶 的「應分潤金額」(月分紅)*/
                            $available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 10, $share_cv);
                            $share_cv_allocated += $available_cv;
                            $BonusHelper->add_available_cv($member_month_divided_id, $available_cv);

                            //REVIEW: 中心發起者佔比..在 中心 的「應分潤金額」部分已經考慮進去了..所以這裡不需要再算了..

                            /*未分配CV金額給系統回收*/
                            $recycle_num = $share_cv - $share_cv_allocated;
                            $BonusHelper->add_available_cv($member_system_id, $recycle_num);
                        } else { /*商品套用「廣告」，進行「總部消費回饋」*/
                            /*再調整「廣告」消費商品的「校正CV金額」(僅投入對應商品模組的「廣告推廣獎勵」的量)*/
                            $count_cv = $BonusHelper->count_ad_available_cv($product['bonus_model_id'], $count_cv);
                            $BonusHelper->add_pi_pool($count_cv); /*累計到資金池異動*/
                            /*取得商品「分享CV金額」*/
                            $share_cv = $BonusHelper->count_share_cv($count_cv);

                            /*排除購買者後取得有效合夥人加權結果(內會組織有效合夥人)*/
                            $weight_result = $BonusHelper->get_active_partner_weight_result($user_id);
                            // dump($weight_result);

                            /*計算各有效合夥人可分金額*/
                            $total_weight = $weight_result['total_weight'];
                            $active_partner = $weight_result['user_weight'];
                            if ($total_weight > 0) {
                                foreach ($active_partner as $partner_id => $weight) {
                                    $available_cv = (float)($share_cv * $weight / $total_weight);
                                    $BonusHelper->add_available_cv($partner_id, $available_cv);
                                }
                            }
                        }
                    }
                }

                /*處理「個人點數(GV)升級」*/
                /*根據GV值自動更新會員級別(購買者)*/
                $vipe_type_id = $BonusHelper->get_vipe_type_by_gv($user_id)['id'] ?? 0;
                $BonusHelper->set_final_vip_type($user_id, $vipe_type_id, 1);

                /*根據GV值自動更新會員級別(購買者的推薦者)*/
                $vipe_type_id2 = $BonusHelper->get_vipe_type_by_gv($upline_user)['id'] ?? 0;
                $BonusHelper->set_final_vip_type($upline_user, $vipe_type_id2, 1);

                /*依統計結果處理回饋*/
                $BonusHelper->send_by_cal('線上消費回饋');
            } catch (\Exception $e) {
                /*發生錯誤時回滾訂單標記*/
                Db::connection('main_db')->table('orderform')->where('id', $orderform['id'])->update(['do_award_time' => '']);
                Db::connection('main_db')->table('orderform_product')->where('orderform_id', $orderform['id'])->where('supplier_bonus', 1)->update(['do_award_supplier_time' => '']);

                /*重新拋出異常*/
                throw $e;
            }
        }
    }

    /**
     * 清理數值，移除千分位逗號和其他非數字字符
     */
    public static function cleanNumericValue($value)
    {
        // 如果已經是數字，直接返回
        if (is_numeric($value)) {
            return (float) $value;
        }

        // 如果是字串，移除千分位逗號和其他非數字字符
        if (is_string($value)) {
            // 移除千分位逗號
            $cleaned = str_replace(',', '', $value);
            // 移除多餘的空格
            $cleaned = trim($cleaned);
            // 確保是有效的數字格式
            if (is_numeric($cleaned)) {
                return (float) $cleaned;
            }
        }

        // 如果無法轉換，返回 0
        return 0.0;
    }

    /**
     * 根據課程類型計算分段CV金額
     *
     * @param array $product 商品信息
     * @param int $newVipLevel 購買課程的級別
     * @param float $totalCV 總CV金額
     * @param \App\Services\pattern\BonusHelper $BonusHelper BonusHelper實例
     * @return array 分段信息數組
     */
    private static function calculateCourseSegments($product, $newVipLevel, $totalCV, $BonusHelper)
    {
        /*
         * 根據需求文檔的規則：
         * 1. 任督課程(vip_type_id=2): 任督burn_cv，由任督課程合伙人依權值分
         * 2. 中脈課程(vip_type_id=3):
         *    - 任督burn_cv 由任督課程合伙人依權值分
         *    - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
         * 3. 法身課程(vip_type_id=4):
         *    - 任督burn_cv 由任督課程合伙人依權值分
         *    - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
         *    - (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
         * 4. 弟子課程(vip_type_id=5):
         *    - 任督burn_cv 由任督課程合伙人依權值分
         *    - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
         *    - (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
         *    - 余額 由弟子課程的合伙人依權值分配
         */

        $segments = [];
        $product_cv = $product['price_cv'] ?? 0;

        /*從BonusHelper獲取各級別的burn_cv值*/
        $rendu_burn_cv = $BonusHelper->arr_vip_types[2]['burn_cv'] ?? 1320; // 任督級別
        $zhongmai_burn_cv = $BonusHelper->arr_vip_types[3]['burn_cv'] ?? 3740; // 中脈級別
        $fashen_burn_cv = $BonusHelper->arr_vip_types[4]['burn_cv'] ?? 4950; // 法身級別

        switch ($newVipLevel) {
            case 2: // 任督課程
                /*任督課程：任督burn_cv 由任督以上課程合伙人分配*/
                $segments[] = [
                    'cv' => $totalCV,
                    'required_level' => 2, // 任督級別
                    'description' => '任督課程CV'
                ];
                break;

            case 3: // 中脈課程
                /*中脈課程分段分配*/
                // 任督burn_cv 由任督課程合伙人依權值分
                $rendu_cv = min($rendu_burn_cv, $product_cv);
                $rendu_ratio = $product_cv > 0 ? $rendu_cv / $product_cv : 0;
                $segments[] = [
                    'cv' => $totalCV * $rendu_ratio,
                    'required_level' => 2, // 任督級別
                    'description' => '任督CV部分'
                ];

                // (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
                if ($product_cv > $rendu_burn_cv) {
                    $zhongmai_diff_cv = min($zhongmai_burn_cv - $rendu_burn_cv, $product_cv - $rendu_burn_cv);
                    $zhongmai_ratio = $product_cv > 0 ? $zhongmai_diff_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $zhongmai_ratio,
                        'required_level' => 3, // 中脈級別
                        'description' => '中脈CV差額部分'
                    ];
                }
                break;

            case 4: // 法身課程
                /*法身課程分段分配*/
                // 任督burn_cv 由任督課程合伙人依權值分
                $rendu_cv = min($rendu_burn_cv, $product_cv);
                $rendu_ratio = $product_cv > 0 ? $rendu_cv / $product_cv : 0;
                $segments[] = [
                    'cv' => $totalCV * $rendu_ratio,
                    'required_level' => 2, // 任督級別
                    'description' => '任督CV部分'
                ];

                // (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
                if ($product_cv > $rendu_burn_cv) {
                    $zhongmai_diff_cv = min($zhongmai_burn_cv - $rendu_burn_cv, $product_cv - $rendu_burn_cv);
                    $zhongmai_ratio = $product_cv > 0 ? $zhongmai_diff_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $zhongmai_ratio,
                        'required_level' => 3, // 中脈級別
                        'description' => '中脈CV差額部分'
                    ];
                }

                // (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
                if ($product_cv > $zhongmai_burn_cv) {
                    $fashen_diff_cv = min($fashen_burn_cv - $zhongmai_burn_cv, $product_cv - $zhongmai_burn_cv);
                    $fashen_ratio = $product_cv > 0 ? $fashen_diff_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $fashen_ratio,
                        'required_level' => 4, // 法身級別
                        'description' => '法身CV差額部分'
                    ];
                }
                break;

            case 5: // 弟子課程
                /*弟子課程分段分配*/
                // 任督burn_cv 由任督課程合伙人依權值分
                $rendu_cv = min($rendu_burn_cv, $product_cv);
                $rendu_ratio = $product_cv > 0 ? $rendu_cv / $product_cv : 0;
                $segments[] = [
                    'cv' => $totalCV * $rendu_ratio,
                    'required_level' => 2, // 任督級別
                    'description' => '任督CV部分'
                ];

                // (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
                if ($product_cv > $rendu_burn_cv) {
                    $zhongmai_diff_cv = min($zhongmai_burn_cv - $rendu_burn_cv, $product_cv - $rendu_burn_cv);
                    $zhongmai_ratio = $product_cv > 0 ? $zhongmai_diff_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $zhongmai_ratio,
                        'required_level' => 3, // 中脈級別
                        'description' => '中脈CV差額部分'
                    ];
                }

                // (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
                if ($product_cv > $zhongmai_burn_cv) {
                    $fashen_diff_cv = min($fashen_burn_cv - $zhongmai_burn_cv, $product_cv - $zhongmai_burn_cv);
                    $fashen_ratio = $product_cv > 0 ? $fashen_diff_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $fashen_ratio,
                        'required_level' => 4, // 法身級別
                        'description' => '法身CV差額部分'
                    ];
                }

                // 余額 由弟子課程的合伙人依權值分配
                if ($product_cv > $fashen_burn_cv) {
                    $dizi_remaining_cv = $product_cv - $fashen_burn_cv;
                    $dizi_ratio = $product_cv > 0 ? $dizi_remaining_cv / $product_cv : 0;
                    $segments[] = [
                        'cv' => $totalCV * $dizi_ratio,
                        'required_level' => 5, // 弟子級別
                        'description' => '弟子CV余額部分'
                    ];
                }
                break;

            default:
                /*其他課程類型不適用此規則，全部分配給所有合伙人*/
                $segments[] = [
                    'cv' => $totalCV,
                    'required_level' => 1, // 最低級別
                    'description' => '一般課程CV'
                ];
                break;
        }

        return $segments;
    }

    /**
     * 分配合夥平級獎勵給直接上線合夥人
     *
     * @param BonusHelper $BonusHelper
     * @param array $partner_ids 獲得推廣獎勵的合夥人ID陣列
     * @param float $base_cv 應分配的基礎CV金額
     */
    private static function distributePartnerBonusToDirectUplines($BonusHelper, array $partner_ids, float $base_cv)
    {
        // 收集所有直接上線合夥人
        $upline_partners = [];

        foreach ($partner_ids as $partner_id) {
            // 獲取直接上線
            $upline_user_id = $BonusHelper->get('user_cal')[$partner_id]['data']['upline_user'] ?? 0;

            if ($upline_user_id > 0) {
                // 初始化上線用戶數據
                $BonusHelper->init_user_set($upline_user_id);

                // 檢查上線是否為合夥人
                $upline_partner_level_id = $BonusHelper->get('user_cal')[$upline_user_id]['data']['partner_level_id'] ?? 0;

                if ($upline_partner_level_id > 0) {
                    $upline_partners[$upline_user_id] = $upline_partner_level_id; // 使用user_id作為key避免重複
                }
                Log::info("平級合夥人", ['取得推廣奬' => $partner_id, '上線' => $upline_user_id, '合夥人等級' => $upline_partner_level_id]);
            }
        }

        if (empty($upline_partners)) {
            Log::info("平級合夥人, 沒有上線合夥人");
            return; // 沒有上線合夥人，直接返回
        }

        // 計算總權重
        $total_weight = 0;
        $partner_weights = [];

        foreach ($upline_partners as $upline_id => $partner_level_id) {
            $partner_level = $BonusHelper->get('arr_partner_levels')[$partner_level_id] ?? [];
            $partner_bonus_ratio = ($partner_level['partner_bonus_ratio'] ?? 0) / 100; // 轉換為小數

            if ($partner_bonus_ratio > 0) {
                $partner_weights[$upline_id] = $partner_bonus_ratio;
                $total_weight += $partner_bonus_ratio;
            }
        }

        // 如果沒有有效的權重，直接返回
        if ($total_weight <= 0) {
            Log::info("平級合夥人, 沒有有效的權重");
            return;
        }

        // 按權重分配CV
        foreach ($partner_weights as $upline_id => $weight) {
            $allocated_cv = $base_cv * ($weight / $total_weight);
            $BonusHelper->add_available_cv($upline_id, $allocated_cv);
            Log::info("平級合夥人分配", ['上線' => $upline_id, '分配CV' => $allocated_cv]);
        }
    }
}
